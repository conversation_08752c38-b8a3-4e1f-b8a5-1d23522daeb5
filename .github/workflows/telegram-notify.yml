name: Notify Telegram on Push to dev

on:
  push:
    branches:
      - new-ui/dev
      - release
      - staging
      - prod

jobs:
  notify:
    runs-on: ubuntu-latest

    steps:
    - name: Send message to Telegram
      env:
        TELEGRAM_TOKEN: ${{ secrets.TELEGRAM_TOKEN }}
        TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
      run: |
        MESSAGE="A push has been made to the ${{ github.ref_name }} branch in the repository: $GITHUB_REPOSITORY"
        curl -s -X POST https://api.telegram.org/bot$TELEGRAM_TOKEN/sendMessage -d chat_id=$TELEGRAM_CHAT_ID -d text="$MESSAGE"
