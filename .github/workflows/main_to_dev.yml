name: Auto Merge Main to Dev

on:
  push:
    branches:
      - main

jobs:
  merge-and-create-pr:
    runs-on: ubuntu-latest
    steps:
      # Bước 1: Checkout code
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Important for merging

      # Bước 2: <PERSON><PERSON><PERSON> hình git
      - name: Setup Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "hungphandinh"

      # Bước 3: Merge nhánh release vào staging
      - name: Merge main into new-ui/dev
        run: |
          git fetch origin new-ui/dev
          git checkout new-ui/dev
          git merge --no-ff origin/main -m "Merge main into new-ui/dev [skip ci]"
          git push origin new-ui/dev
