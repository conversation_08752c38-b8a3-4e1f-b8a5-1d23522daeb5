import { getAllTool, getAllToolAvailable, getToolMostUsed, getWelcomeTool } from "@services/Tool";
import { getToolStudent } from "@src/app/services/Settings";
import { put, select, takeLatest } from "redux-saga/effects";
import { CONSTANT, TOOL_CATEGORY } from "@constant";
import { cloneObj } from "@common/functionCommons";

export const actionTypes = {
  GetTool: "Tool/GetTool",
  SetTool: "Tool/SetTool",
  
  SetToolData: "Tool/SetToolData",
  
  GetSuggestTool: "Tool/GetSuggestTool",
  SetSuggestTool: "Tool/SetSuggestTool",
  
  GetToolAvailable: "Tool/GetToolAvailable",
  SetToolAvailable: "Tool/SetToolAvailable",
  
  GetUserTool: "Tool/GetUserTool",
  SetUserTool: "Tool/SetUserTool",
  
  GetMostUsed: "Tool/GetMostUsed",
  SetMostUsed: "Tool/SetMostUsed",
  ClearMostUsed: "Tool/ClearMostUsed",
  
  UpdateFavTool: "Tool/UpdateFavTool",
  
  LocalizeTool: "Tool/LocalizeTool",
  
  GetStudentTool: "Tool/GetStudentTool",
  SetStudentTool: "Tool/SetStudentTool",
};

const initialAuthState = {
  toolData: undefined,
  mostUsedToolData: undefined,
  listAllTool: undefined,
  listToolAvailable: undefined,
  suggestToolData: undefined,
  studentTools: undefined,
};

export const reducer = (state = initialAuthState, action) => {
  switch (action.type) {
    case actionTypes.SetMostUsed: {
      const { mostUsedToolData } = action.payload;
      return Object.assign({}, state, { mostUsedToolData });
    }
    case actionTypes.ClearMostUsed: {
      return Object.assign({}, state, { mostUsedToolData: [] });
    }
    case actionTypes.SetSuggestTool: {
      const { suggestToolData } = action.payload;
      return Object.assign({}, state, { suggestToolData });
    }
    case actionTypes.SetTool: {
      const { listAllTool } = action.payload;
      return Object.assign({}, state, { listAllTool });
    }
    case actionTypes.SetToolData: {
      const { toolData } = action.payload;
      return Object.assign({}, state, { toolData });
    }
    case actionTypes.SetToolAvailable: {
      const { toolList, user } = action.payload;
      const organizationTool = [];
      const includeGroupTools = toolList.filter(tool => tool.groupToolIds?.length);
      const toolObj = includeGroupTools.reduce(function (grouped, element) {
        grouped.favorite ||= [];
        const categoryData = element.categories?.split(",")?.map(item => item?.trim()).filter(item => !!item);
        categoryData.forEach(category => {
          grouped[category] ||= [];
          grouped[category].push(element);
        });
        if (element.isFavorite) {
          grouped.favorite.push(element);
        }
        if (user.organizationId && element.isOrganizationTool) {
          organizationTool.push(element);
        }
        return grouped;
      }, {});
      if (organizationTool.length) {
        toolObj.organization = organizationTool;
      }
      const toolData = {};
      Object.entries(toolObj).forEach(([key, value]) => {
        const category = key.toUpperCase();
        toolData[category] = { ...TOOL_CATEGORY[category], toolList: value };
      });
      return Object.assign({}, state, { toolData, listToolAvailable: includeGroupTools });
    }
    case actionTypes.UpdateFavTool: {
      const { toolId, isFavorite } = action.payload;
      const toolData = { ...state.toolData };
      toolData[CONSTANT.FAVORITE].toolList ||= [];
      let updateFavoriteTool = {};
      
      //update category tools
      for (const [key, value] of Object.entries(toolData)) {
        if (key === CONSTANT.FAVORITE) continue;
        let checkExist = false;
        for (const tool of value.toolList) {
          if (tool._id === toolId) {
            tool.isFavorite = isFavorite;
            updateFavoriteTool = tool;
            checkExist = true;
            break;
          }
        }
        if (checkExist) break;
      }
      
      //update favorite tools
      if (isFavorite) {
        toolData[CONSTANT.FAVORITE].toolList.push(updateFavoriteTool);
      } else {
        toolData[CONSTANT.FAVORITE].toolList = toolData[CONSTANT.FAVORITE].toolList.filter(tool => tool._id !== toolId);
      }
      toolData[CONSTANT.FAVORITE].toolList.sort((a, b) => {
        const toolNameA = a?.name?.en || a?.name?.vi || a?.name;
        const toolNameB = b?.name?.en || b?.name?.vi || b?.name;
        return toolNameA.localeCompare(toolNameB);
      });
      
      // update favorite mostUsedToolData
      const mostUsedToolData = cloneObj(state.mostUsedToolData)
        ?.filter(tool => !!tool)
        ?.map(tool => {
          if (tool._id === toolId) tool.isFavorite = isFavorite;
          return tool;
        });
      
      return Object.assign({}, state, { toolData, mostUsedToolData });
    }
    case actionTypes.SetStudentTool: {
      const { studentTools } = action.payload;
      return Object.assign({}, state, { studentTools });
    }
    default:
      return state;
  }
};

export const actions = {
  getToolAvailable: () => ({ type: actionTypes.GetToolAvailable }),
  setToolAvailable: (toolList, user) => ({ type: actionTypes.SetToolAvailable, payload: { toolList, user } }),
  getTool: () => ({ type: actionTypes.GetTool }),
  setTool: (listAllTool) => ({ type: actionTypes.SetTool, payload: { listAllTool } }),
  
  setToolData: (toolData) => ({ type: actionTypes.SetToolData, payload: { toolData } }),
  
  getSuggestTool: () => ({ type: actionTypes.GetSuggestTool }),
  setSuggestTool: (suggestToolData) => ({ type: actionTypes.SetSuggestTool, payload: { suggestToolData } }),
  
  getMostUsedTool: (toolType) => ({ type: actionTypes.GetMostUsed, payload: { toolType } }),
  setMostUsedTool: (mostUsedToolData) => ({ type: actionTypes.SetMostUsed, payload: { mostUsedToolData } }),
  clearMostUsedTool: () => ({ type: actionTypes.ClearMostUsed }),
  
  updateFavTool: (toolId, isFavorite) => ({ type: actionTypes.UpdateFavTool, payload: { toolId, isFavorite } }),
  
  localizeTool: () => ({ type: actionTypes.LocalizeTool }),
  
  getStudentTool: () => ({ type: actionTypes.GetStudentTool }),
  setStudentTool: studentTools => ({ type: actionTypes.SetStudentTool, payload: { studentTools } }),
};

export function* saga() {
  yield takeLatest(actionTypes.GetToolAvailable, function* getToolSaga() {
    const language = yield select(store => store.app.language);
    const user = yield select(store => store.auth.user);
    const dataResponse = yield getAllToolAvailable();
    if (dataResponse) {
      const toolList = translateToolList(dataResponse, language);
      yield put(actions.setToolAvailable(toolList, user));
    }
  });
  
  yield takeLatest(actionTypes.GetTool, function* getToolSaga() {
    const dataResponse = yield getAllTool();
    if (dataResponse) {
      const language = yield select(store => store.app.language);
      yield put(actions.setTool(translateToolList(dataResponse, language)));
    }
  });
  
  yield takeLatest(actionTypes.GetSuggestTool, function* getSuggestToolSaga() {
    const language = yield select(store => store.app.language);
    const dataResponse = yield getWelcomeTool();
    if (dataResponse) {
      Object.values(dataResponse).forEach(value => {
        value.tools?.forEach(tool => translateToolData(tool, language));
      });
      yield put(actions.setSuggestTool(dataResponse));
    }
  });
  
  yield takeLatest(actionTypes.GetMostUsed, function* getMostUsedSaga({ payload }) {
    const language = yield select(store => store.app.language);
    const dataResponse = yield getToolMostUsed(payload.toolType);
    if (Array.isArray(dataResponse)) {
      yield put(actions.setMostUsedTool(dataResponse.map(tool => translateToolData(tool, language))));
    }
  });
  
  yield takeLatest(actionTypes.LocalizeTool, function* localizeToolSaga() {
    const language = yield select(store => store.app.language);
    const user = yield select(store => store.auth.user);
    const toolStore = yield select(store => store.tool);
    const { suggestToolData, mostUsedToolData, studentTools } = toolStore;
    
    // translate all tool
    if (toolStore.listAllTool) {
      const listAllTool = translateToolList(toolStore.listAllTool, language);
      yield put(actions.setTool(listAllTool));
    }
    
    // translate tool available
    if (toolStore.listToolAvailable) {
      const listToolAvailable = translateToolList(toolStore.listToolAvailable, language);
      yield put(actions.setToolAvailable(listToolAvailable, user));
    }
    
    // translate tool data
    if (toolStore.toolData) {
      const toolData = { ...toolStore.toolData };
      Object.values(toolData).forEach(value => {
        value.toolList = value.toolList?.map(tool => translateToolData(tool, language));
      });
      yield put(actions.setToolData(toolData));
    }
    
    
    // translate suggest tools
    if (suggestToolData) {
      Object.values(suggestToolData).forEach(value => {
        value.tools?.forEach(tool => translateToolData(tool, language));
      });
      yield put(actions.setSuggestTool(suggestToolData));
    }
    
    // translate most used tool tools
    if (mostUsedToolData) {
      yield put(actions.setMostUsedTool(mostUsedToolData.map(tool => translateToolData(tool, language))));
    }

    // translate student tools
    if (studentTools) {

      Object.values(studentTools).forEach(value => {
        if (Array.isArray(value)) {
          value.forEach(tool => translateToolData(tool, language));
        } else {
          //translateToolData(value, language);
        }
      });
      yield put(actions.setStudentTool(studentTools));
    }
  });
  
  yield takeLatest(actionTypes.GetStudentTool, function* getStudentTool() {
    const dataResponse = yield getToolStudent();
    if (dataResponse) {
      const language = yield select(store => store.app.language);
      
      Object.values(dataResponse).forEach(value => {
        if (Array.isArray(value)) {
          value.forEach(tool => translateToolData(tool, language));
        } else {
          //translateToolData(value, language);
        }
      });
      yield put(actions.setStudentTool(dataResponse));
    }
  });
}


function translateToolList(toolList, lang) {
  if (!Array.isArray(toolList)) return toolList;
  return toolList.map(tool => translateToolData(tool, lang));
}

function translateToolData(toolItem, lang) {
  if (!lang) return toolItem;
  // toolItem = { ...toolItem };
  if (toolItem?.localization) {
    Object.entries(toolItem.localization).forEach(([key, value]) => {
      if (value?.[lang]) {
        toolItem[key] = value[lang];
      }
    });
  }
  
  //for group tool
  toolItem?.groupToolIds?.forEach(group => {
    if (group?.localization) {
      Object.entries(group?.localization).forEach(([key, value]) => {
        if (value?.[lang]) {
          group[key] = value[lang];
        }
      });
    }
  });
  return toolItem;
}