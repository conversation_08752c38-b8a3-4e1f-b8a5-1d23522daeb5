import { getAllRoles } from '@src/app/services/Role';
import { put, takeLatest } from 'redux-saga/effects';

export const actionTypes = {
  GetRole: 'Role/GetRole',
  SetRole: 'Role/SetRole',
};

const initialAuthState = {
  roleList: undefined,
};

export const reducer = (state = initialAuthState, action) => {
  switch (action.type) {
    case actionTypes.SetRole: {
      const { roleList } = action.payload;
      return Object.assign({}, state, { roleList });
    }
    default:
      return state;
  }
};

export const actions = {
  getRole: () => ({ type: actionTypes.GetRole }),
  setRole: roleList => ({ type: actionTypes.SetRole, payload: { roleList } }),
};

export function* saga() {
  yield takeLatest(actionTypes.GetRole, function* getRoleSaga() {
    const dataResponse = yield getAllRoles();
    if (dataResponse) {
      yield put(actions.setRole(dataResponse));
    }
  });
}
