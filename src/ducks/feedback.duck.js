import { getAllGroupFeedback } from "@src/app/services/Feedback";
import { put, takeLatest } from 'redux-saga/effects';

export const actionTypes = {
  GetGroupFeedback: 'Feedback/GetGroupFeedback',
  SetGroupFeedback: 'Feedback/SetGroupFeedback',
};

const initialGroupFeedbackState = {
  groupFeedback: undefined,
};

export const reducer = (state = initialGroupFeedbackState, action) => {
  switch (action.type) {
    case actionTypes.SetGroupFeedback: {
      const { groupFeedback } = action.payload;
      return Object.assign({}, state, { groupFeedback });
    }
    default:
      return state;
  }
};

export const actions = {
  getGroupFeedback: () => ({ type: actionTypes.GetGroupFeedback }),
  setGroupFeedback: groupFeedback => ({ type: actionTypes.SetGroupFeedback, payload: { groupFeedback } }),
};

export function* saga() {
  yield takeLatest(actionTypes.GetGroupFeedback, function* getGroupFeedbackSaga() {
    const dataResponse = yield getAllGroupFeedback();
    if (dataResponse) {
      yield put(actions.setGroupFeedback(dataResponse));
    }
  });
}
