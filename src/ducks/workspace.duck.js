import { getAvailableWorkspace } from '@services/Workspace';
import { put, takeLatest } from 'redux-saga/effects';

export const actionTypes = {
  GetAvailableWorkspaces: 'Workspace/GetAvailableWorkspaces',
  SetAvailableWorkspaces: 'Workspace/SetAvailableWorkspaces',
};

const initialWorkspaceState = {
  availableWorkspaces: undefined,
};

export const reducer = (state = initialWorkspaceState, action) => {
  switch (action.type) {
    case actionTypes.SetAvailableWorkspaces: {
      const { workspaces } = action.payload;
      return Object.assign({}, state, { availableWorkspaces: workspaces });
    }
    default:
      return state;
  }
};

export const actions = {
  getAvailableWorkspaces: () => ({ type: actionTypes.GetAvailableWorkspaces }),
  setAvailableWorkspaces: workspaces => ({ type: actionTypes.SetAvailableWorkspaces, payload: { workspaces } }),
};

export function* saga() {
  yield takeLatest(actionTypes.GetAvailableWorkspaces, function* getAvaiableWorkspacesSaga() {
    const dataResponse = yield getAvailableWorkspace();
    if (dataResponse) {
      yield put(actions.setAvailableWorkspaces(dataResponse));
    }
  });
}
