import { useEffect, useMemo } from "react";
import { Link, matchPath, useLocation } from "react-router-dom";
import clsx from "clsx";
import { connect, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";

import { LINK } from "@link";

import "./HeaderStudentMenu.scss";

import * as toolRedux from "@src/ducks/tool.duck";
import { actions, TRACKING_SCREENS } from "@src/ducks/tracking.duck";

const HeaderStudentMenu = ({ studentTools, ...props }) => {
  const location = useLocation();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  useEffect(() => {
    if (!studentTools) {
      props.getStudentTool();
    }
  }, []);

  const studentMenu = useMemo(() => {
    if (!studentTools) return [];

    const studyHublinks = [];
    if (studentTools.writing.length) studyHublinks.push(LINK.WRITING, LINK.WRITING_RESULT.format(":id"));
    if (studentTools.speaking) studyHublinks.push(LINK.SPEAKING, LINK.SPEAKING_ID.format(":id"));
    if (studentTools.speaking) studyHublinks.push(LINK.DICTATION_SHADOWING, LINK.DICTATION_SHADOWING.format(":id"));

    return [
      { lang: 'STUDY_HUB', links: studyHublinks },
      { lang: "PRICING", links: [LINK.PRICING] },
    ];
  }, [studentTools]);

  return (
    <div className="header-student-menu">
      {studentMenu.map((item, index) => {
        const isActive = item.links.some((link) => !!matchPath(link, location.pathname));
        const linkTo = item.links[0];
        return (
          <Link
            to={linkTo}
            className={clsx("header-student-menu__item", { "header-student-menu__item-active": isActive })}
            key={index}
            onClick={() => {
              dispatch(actions.trackClickNavigation(TRACKING_SCREENS.STUDY_HUB))
            }}
          >
            {t(item.lang)}
          </Link>
        );
      })}
    </div>
  );
};
const mapStateToProps = (state) => ({
  studentTools: state.tool.studentTools,
});

const mapDispatchToProps = {
  ...toolRedux.actions,
};

export default (connect(mapStateToProps, mapDispatchToProps)(HeaderStudentMenu));