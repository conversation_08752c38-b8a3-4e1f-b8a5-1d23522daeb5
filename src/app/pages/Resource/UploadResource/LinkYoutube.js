import React, { useEffect, useState } from "react";
import { Form, Input } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import { useResource } from "@app/pages/Resource";

import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";
import RULE from "@rule";

import { cloneObj, getYoutubeVideoId } from "@common/functionCommons";
import { createResourceYoutubeVideo } from "@services/Resource";

import Close from "@component/SvgIcons/Close";
import Plus20 from "@component/SvgIcons/Plus/Plus20";
import { toast } from "@component/ToastProvider";

const URL_STATUS_INIT = { success: null, isNew: true };

function LinkYoutube({ user }) {
  const { t } = useTranslation();
  const { setMyResourceData, setOrgResourceData, resourceTypeActive } = useResource();
  const { categorySelected, isShowUpload, setShowUpload } = useResource();
  
  const [isLoading, setLoading] = useState(false);
  const [urlStatus, setUrlStatus] = useState([]);
  
  const [formLinkYoutube] = Form.useForm();
  
  useEffect(() => {
    formLinkYoutube.validateFields();
  }, [urlStatus]);
  
  useEffect(() => {
    if (isShowUpload) {
      formLinkYoutube.setFieldsValue({ links: [""] });
      setUrlStatus([URL_STATUS_INIT]);
    }
  }, [isShowUpload]);
  
  
  const onFinish = async (values) => {
    const { links } = values;
    
    if (Array.isArray(links)) {
      const isExistNull = links.findIndex((element) => !element) !== -1;
      if (isExistNull) {
        setUrlStatus(prevState => {
          return cloneObj(prevState).map(state => {
            delete state.isNew;
            return state;
          });
        });
        return;
      }
      
      setLoading(true);
      let allSuccess = true;
      for (let i = 0; i < links.length; i++) {
        const success = await handleCreateResource(links[i], i);
        if (!success) {
          allSuccess = false;
        }
      }
      setLoading(false);
      if (allSuccess) {
        toast.success("CREATE_RESOURCE_SUCCESS");
        setShowUpload(false);
      }
    }
  };
  
  async function handleCreateResource(url, index) {
    const apiRequest = { url };
    if (categorySelected === CONSTANT.MY_RESOURCE) {
      apiRequest.userId = user._id;
    } else if (categorySelected === CONSTANT.ORG_RESOURCE) {
      apiRequest.organizationId = user.organizationId?._id;
    }
    
    
    const apiResponse = await createResourceYoutubeVideo(apiRequest);
    
    if (apiResponse.success) {
      if (categorySelected === CONSTANT.MY_RESOURCE) {
        setMyResourceData(prevState => {
          const newState = cloneObj(prevState);
          newState[resourceTypeActive] ||= [];
          newState[resourceTypeActive].push(apiResponse.data);
          return newState;
        });
      } else if (categorySelected === CONSTANT.ORG_RESOURCE) {
        setOrgResourceData(prevState => {
          const newState = cloneObj(prevState);
          newState[resourceTypeActive] ||= [];
          newState[resourceTypeActive].push(apiResponse.data);
          return newState;
        });
      }
    } else {
      setUrlStatus(prevState => {
        const newState = cloneObj(prevState);
        newState[index] = apiResponse;
        return newState;
      });
    }
    return apiResponse.success;
  }
  
  return <>
    <AntForm
      form={formLinkYoutube}
      name="youtube-link-form"
      onFinish={onFinish}
    >
      <Form.List name="links">
        {(fields, { add, remove }) => (
          <>
            <Loading active={isLoading} className="youtube-link__list">
              {fields.map((field, index) => {
                return <div key={field.key} className="youtube-link__item">
                  <AntForm.Item
                    name={[field.name]}
                    validateTrigger={["onChange", "onBlur"]}
                    rules={[
                      () => ({
                        validator(_, value) {
                          const fieldStatus = urlStatus[field.name];
                          if (fieldStatus) {
                            if (!fieldStatus.success && fieldStatus.message) {
                              return Promise.reject(new Error(fieldStatus.message));
                            }
                          }
                          if (!fieldStatus.isNew && (!value || !value?.trim())) {
                            return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                          }
                          if (value && (!getYoutubeVideoId(value) || !RULE.YOUTUBE_URL.pattern.test(value))) {
                            return Promise.reject(new Error(t("INVALID_ADDRESS")));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                    className="youtube-link__item-input"
                  >
                    <Input
                      size="large"
                      placeholder={t("PASTE_YOUR_YOUTUBE_VIDEO_URL_HERE")}
                      onChange={() => {
                        if (urlStatus[index].isNew) {
                          setUrlStatus(prevState => {
                            const newState = cloneObj(prevState);
                            delete newState[index].isNew;
                            return newState;
                          });
                        }
                        if (!urlStatus[index].success && urlStatus[index].message) {
                          setUrlStatus(prevState => {
                            const newState = cloneObj(prevState);
                            newState[index] = { success: null };
                            return newState;
                          });
                        }
                      }}
                    />
                  </AntForm.Item>
                  
                  <AntButton
                    size="small"
                    className="youtube-link__item-remove"
                    type={BUTTON.WHITE}
                    icon={<Close />}
                    shape="circle"
                    onClick={() => {
                      remove(field.name);
                      setUrlStatus(prevState => prevState.filter((_, index) => index !== field.name));
                    }}
                  />
                </div>;
              })}
            </Loading>
            
            <div className="youtube-link__item-add">
              <AntButton
                size="large"
                type={BUTTON.WHITE_NAVI}
                onClick={() => {
                  add();
                  setUrlStatus(prevState => [...prevState, URL_STATUS_INIT]);
                }}
                icon={<Plus20 />}
                shape="circle"
                disabled={isLoading}
              />
            </div>
            
            <div className="youtube-link__submit">
              <AntButton
                size="large"
                onClick={() => setShowUpload(false)}
                disabled={isLoading}
              >
                {t("CANCEL")}
              </AntButton>
              <AntButton
                size="large"
                type={BUTTON.DEEP_NAVY}
                disabled={!fields.length}
                htmlType="submit"
                loading={isLoading}
              >
                {t("SAVE")}
              </AntButton>
            </div>
          </>
        )}
      </Form.List>
    </AntForm>
  
  </>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(LinkYoutube);