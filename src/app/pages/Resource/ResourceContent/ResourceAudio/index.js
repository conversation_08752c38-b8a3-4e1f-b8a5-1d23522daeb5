import React, { useEffect, useState } from "react";
import { Dropdown, Table } from "antd";
import { useTranslation } from "react-i18next";

import { useResource } from "@app/pages/Resource";

import NoData from "@component/NoData";
import ResourceAudioPlayer from "./ResourceAudioPlayer";

import { BUTTON, CONSTANT } from "@constant";

import { getFileExtension, formatTimeDate } from "@common/functionCommons";

import Trash from "@component/SvgIcons/Trash";
import AntButton from "@component/AntButton";
import MoreVertical from "@component/SvgIcons/MoreVertical";

import "./ResourceAudio.scss";

function ResourceAudio() {
  const { t } = useTranslation();
  
  const { resourceData, resourceTypeActive, resourceSearchValue, handleDeleteResource } = useResource();
  const [audioList, setAudioList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // Number of items per page
  
  
  useEffect(() => {
    if (resourceTypeActive === CONSTANT.AUDIO && currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [resourceSearchValue]);
  
  useEffect(() => {
    setAudioList(() => {
      if (!resourceSearchValue) return resourceData?.[CONSTANT.AUDIO];
      return resourceData?.[CONSTANT.AUDIO]?.filter(x => x?.name?.toLowerCase()?.includes(resourceSearchValue?.toLowerCase()));
    });
  }, [resourceData, resourceSearchValue]);
  
  useEffect(() => {
    if (audioList?.length && (audioList.length / pageSize) === (currentPage - 1)) {
      setCurrentPage(currentPage - 1);
    }
  }, [audioList]);
  
  // todo: responsive col file name
  const columns = [
    {
      title: t("ORDER"),
      width: 70,
      render: (value, row, index) => (currentPage - 1) * pageSize + index + 1,
    },
    {
      title: t("FILE_NAME"),
      dataIndex: "name",
      width: 300,
    },
    { title: t("TYPE"), dataIndex: "name", render: getFileExtension, width: 80 },
    {
      title: t("DATE_MODIFIED"),
      dataIndex: ["updatedAt"],
      render: formatTimeDate,
      width: 155,
    },
    {
      title: t("AUDIO_PLAYER"),
      dataIndex: "audioId",
      width: 180,
      render: (value, row, index) => {
        return <ResourceAudioPlayer
          id={row._id + index}
          resourceData={row}
          audioDuration={value.duration}
        />;
      },
    },
    {
      title: t("ACTION"),
      width: 90,
      render: (value) => {
        return <div className="resource-audio-action">
          <Dropdown
            menu={{
              items: [{
                key: "DELETE",
                label: t("DELETE"),
                icon: <Trash />,
                onClick: () => handleDeleteResource(value),
              }],
              className: "action-dropdown-menu",
            }}
            trigger={["click"]}
            placement="bottomRight"
          >
            <AntButton
              size="small"
              type={BUTTON.GHOST_WHITE}
              icon={<MoreVertical />}
              onClick={(e) => e.stopPropagation()}
            />
          </Dropdown>
        </div>;
      },
    },
  ];
  
  
  if (resourceTypeActive !== CONSTANT.AUDIO) return null;
  if (!audioList?.length) return <NoData />;
  return <>
    <div className="resource-audio-container">
      <Table
        scroll={{ x: "max-content" }}
        dataSource={audioList.slice((currentPage - 1) * pageSize, currentPage * pageSize)}
        columns={columns}
        pagination={{
          current: currentPage,
          total: audioList.length,
          pageSize: pageSize,
          onChange: setCurrentPage,
        }}
      />
    </div>
  
  
  </>;
}

export default ResourceAudio;