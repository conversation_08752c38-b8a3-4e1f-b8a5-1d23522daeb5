import React from "react";
import { Popover } from "antd";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import dayjs from "dayjs";
import clsx from "clsx";

import Starred from "@component/Star";
import ProjectActionsDropdown from "@component/ProjectActionsDropdown";

import { LINK } from "@link";
import { API } from "@api";
import { saved, unsaved } from "@services/MySaved";
import { getProjectIcon } from "@src/app/component/FolderProjectList/FolderProjectItem";

import MoreFolderIcon from "@src/asset/icon/button/MoreFolderIcon";
import PROJECT_DEFAULT from "@src/asset/image/project-default.svg";

const FolderListGrid = ({ ...props }) => {
  const { t } = useTranslation();
  const { projectsData } = props;
  const { handleAfterMove, handleAfterDelete, handleAfterCopy, handleAfterRename, handleAfterChangeStar } = props;

  const onClickStar = async (record) => {
    if (!record.isSaved) {
      const response = await saved(record?._id, null);
      if (response) {
        handleAfterChangeStar(record);
      }
    } else {
      const response = await unsaved(record?._id, null);
      if (response) {
        handleAfterChangeStar(record);
      }
    }
  };

  return (
    <div className="grid-layout grid">
      {projectsData.map((project, index) => {
        const thumbnailFileId = project?.imageId?.thumbnailFileId;
        const imageSrc = thumbnailFileId ? API.STREAM_ID.format(thumbnailFileId) : PROJECT_DEFAULT;
        return (
          <div className={"col-span-1 grid-layout-table-wraper"}>
            <Link to={LINK.PROJECT_DETAIL.format(project?._id)} className="grid-layout-table__content">
              <div className={clsx("grid-layout-table__preview", { "thumbnail-default": !thumbnailFileId })}>
                <img
                  className="grid-layout-table__preview-inner"
                  src={imageSrc}
                  alt=""
                />
                <div className="grid-layout-table__backdrop-hover">{t("VIEW_DETAIL")}</div>
              </div>
            </Link>
            <div className="grid-layout-table__content">
              <div className="grid-layout-table__info">
                <div className="grid-layout-table__title">
                  <img src={getProjectIcon(project)} alt='' className="grid-layout-table__title-icon" />
                  <Popover
                    className="grid-layout-table__title-text"
                    placement="topLeft"
                    title={project?.projectName}
                    trigger="hover"
                  >
                    {project?.projectName}
                  </Popover>
                </div>
                <div className="grid-layout-table__last-modified">{dayjs(project?.updatedAt).fromNow()}</div>
              </div>
              <div className={"grid-layout-table__actions"}>
                <div className="grid-layout-table__btnActions rounded">
                  <ProjectActionsDropdown
                    className="project-dropdown"
                    projectData={project}
                    handleAfterCopy={handleAfterCopy}
                    handleAfterRename={handleAfterRename}
                    handleAfterDelete={handleAfterDelete}
                    handleAfterMove={handleAfterMove}
                    iconActions={<MoreFolderIcon />}
                  />
                </div>
                <div
                  className="starred-content"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Starred
                    active={project?.isSaved}
                    onClick={() => {
                      onClickStar(project);
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default FolderListGrid;
