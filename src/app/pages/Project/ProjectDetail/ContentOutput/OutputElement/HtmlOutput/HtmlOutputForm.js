import clsx from "clsx";
import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";


import { useProject } from "@app/pages/Project";
import { AntForm } from "@component/AntForm";
import CustomCKEditor from "@component/CKEditor";
//import BalloonEditor from "@ckeditor/ckeditor5-build-balloon";
//import InlineEditor from "@ckeditor/ckeditor5-build-inline";
//import MultiRootEditor from "@ckeditor/ckeditor5-build-multi-root";


import RULE from "@rule";

function HtmlOutputForm({ responseSelected, isEditing, setEditing, ...props }) {
  
  const { t } = useTranslation();
  
  const { handleSubmitResponse } = useProject();
  
  const { content, outputForm } = props;
  
  useEffect(() => {
    outputForm.setFieldsValue({ html: responseSelected?.output?.html });
  }, [responseSelected]);
  
  
  useEffect(() => {
    if (!isEditing) {
      outputForm.setFieldsValue({ html: responseSelected?.output?.html });
    }
  }, [isEditing]);
  
  function onFinishResponse(values) {
    handleSubmitResponse(content._id, { _id: responseSelected._id, output: { html: values.html } })
      .then(() => setEditing(false));
  }
  
  return <AntForm
    id={`form-response-${responseSelected._id}`}
    form={outputForm}
    onFinish={onFinishResponse}
    className={clsx("text-output", "js-question-form", { hidden: !isEditing })}
  >
    <AntForm.Item
      name="html"
      rules={[RULE.REQUIRED]}
      //valuePropName="data"
      //getValueFromEvent={(event, editor) => {
      //  return editor.getData();
      //}}
    >
      <CustomCKEditor />
    </AntForm.Item>
  </AntForm>;
}

export default HtmlOutputForm;