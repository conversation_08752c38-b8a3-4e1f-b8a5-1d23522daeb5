import React, { useEffect, useState } from "react";
import { Button, Form, Input } from "antd";
import { useProject } from "@app/pages/Project";

import { CONSTANT } from "@constant";
import { cloneObj } from "@common/functionCommons";
import { handleMoveOutput, updateAbcdQuestionId } from "../../outputCommons";

import ArrowUp from "@component/SvgIcons/ArrowUp";
import ArrowDown from "@component/SvgIcons/ArrowDown";
import TimeIcon from "@component/SvgIcons/TimeIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";


function QuestionForm({ responseSelected, isEditing, setEditing, ...props }) {
  const { handleSubmitResponse } = useProject();
  const { content, outputForm } = props;
  
  const [questionData, setQuestionData] = useState([]);
  
  useEffect(() => {
    if (Array.isArray(responseSelected?.output?.questions)) {
      setQuestionData(responseSelected.output.questions);
    }
  }, [responseSelected]);
  
  function moveQuestion(questionId, type) {
    setQuestionData(prevState => handleMoveOutput(prevState, questionId, type));
  }
  
  function handleChangeQuestion(questionId, value) {
    setQuestionData(prevState => {
      const newState = cloneObj(prevState);
      return newState.map(state => {
        if (state.questionId === questionId) {
          state.question = value;
        }
        return state;
      });
    });
  }
  
  function handleDeleteQuestion(questionId) {
    setQuestionData(prevState => {
      const newState = cloneObj(prevState).filter(state => state.questionId !== questionId);
      return updateAbcdQuestionId(newState);
    });
  }
  
  function handleAddQuestion() {
    setQuestionData(prevState => [...prevState, { questionId: prevState.length + 1 }]);
  }
  
  
  function onFinishResponse() {
    const output = { questions: questionData };
    handleSubmitResponse(content._id, { _id: responseSelected._id, output })
      .then(() => setEditing(false));
  }
  
  return <div className="option-list-output">
    <div className="option-list-output__form-container">
      <Form
        id={`form-response-${responseSelected._id}`}
        className="hidden"
        form={outputForm}
        onFinish={onFinishResponse}
      />
      
      {questionData.map((optionItem, index) => {
        const isLastItem = index === questionData.length - 1;
        return <div key={optionItem.questionId} className="option-list-output__form-item">
          <div className="option-list-output__form-action">
            {!!index && <Button
              icon={<ArrowUp />} className="option-list-output__action-up"
              onClick={() => moveQuestion(optionItem.questionId, CONSTANT.MOVE_UP)}
            />}
            {(questionData.length === 1 || !isLastItem) && <Button
              icon={<ArrowDown />} className="option-list-output__action-down"
              onClick={() => moveQuestion(optionItem.questionId, CONSTANT.MOVE_DOWN)}
              disabled={isLastItem}
            />}
          </div>
          
          <div className="option-list-output__form-content">
            <div className="option-list-content__input">
              <Input.TextArea
                value={optionItem.question}
                onChange={(e) => handleChangeQuestion(optionItem.questionId, e.target.value)}
                autoSize={{ minRows: 1 }}
              />
            </div>
            <div className="option-list-content__action">
              <Button
                size="small" shape="circle" icon={<TimeIcon />}
                onClick={() => handleDeleteQuestion(optionItem.questionId)}
              />
            </div>
          </div>
        </div>;
      })}
      
      <div className="project-content-output__add">
        <Button
          className="btn-add-question"
          shape="circle" icon={<PlusIcon />}
          onClick={handleAddQuestion}
        />
      </div>
    </div>
  </div>;
}

export default QuestionForm;