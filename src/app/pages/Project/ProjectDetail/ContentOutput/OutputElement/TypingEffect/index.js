import React, { memo, useEffect, useMemo, useState } from "react";
import ReactDOMServer from "react-dom/server";

import { useProject } from "@app/pages/Project";
import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";
import { useMarkExam } from "@app/pages/Project/ProjectDetail/MarkExamContent";

import HtmlContent from "@component/HtmlContent";

import { CONSTANT } from "@constant";
import { cloneObj } from "@common/functionCommons";

import worker_script from "./worker-script.js";

const timerWorker = new Worker(worker_script);

const TypingEffect = ({ html }) => {
  const { setProjectContentData, createContentThumbnail } = useProject();
  const contentValues = useContent();
  const markExamValues = useMarkExam();
  
  const content = contentValues?.content || markExamValues?.contentData;
  const responseSelected = contentValues?.responseSelected || markExamValues?.resultSelected;
  
  const [displayHtml, setDisplayHtml] = useState("");
  
  const htmlString = useMemo(() => ReactDOMServer.renderToString(html), [html]);
  
  useEffect(() => {
    timerWorker.postMessage({ status: CONSTANT.START });
  }, [htmlString]);
  
  useEffect(() => {
    timerWorker.onmessage = ({ data: { index } }) => {
      const currentChar = htmlString[index];
      if (!currentChar) {
        stopEffect(true);
      } else {
        setDisplayHtml((prevHtml) => {
          if (currentChar === "<") {
            const tagEndIndex = htmlString.indexOf(">", index);
            const tag = htmlString.substring(index, tagEndIndex + 1);
            timerWorker.postMessage({ status: CONSTANT.NEXT, index: tagEndIndex + 1 });
            return prevHtml + tag;
          } else {
            timerWorker.postMessage({ status: CONSTANT.NEXT, index: index + 1 });
            return prevHtml + currentChar;
          }
        });
      }
    };
    
    return () => stopEffect();
  }, []);
  
  function stopEffect(isCapture = false) {
    timerWorker.postMessage({ status: CONSTANT.STOP });
    setProjectContentData(prevState => {
      const newState = cloneObj(prevState);
      newState.forEach(state => {
        if (state._id === content?._id) {
          state.responses = state.responses.map(response => {
            if (response._id === responseSelected._id) {
              response.typingEffect = false;
            }
            return response;
          });
        }
      });
      return newState;
    });
    
    if (isCapture && content?.contentIndex === 1) {
      createContentThumbnail();
    }
  }
  
  return <HtmlContent dangerouslySetInnerHTML={{ __html: displayHtml }} />;
};


export default memo(TypingEffect);