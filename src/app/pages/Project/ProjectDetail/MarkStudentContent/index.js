import React, { useContext, useEffect, useMemo, useRef, useState } from "react";
import _ from "lodash";
import { useTranslation } from "react-i18next";
import { Form } from "antd";

import { toast } from "@component/ToastProvider";
import { useProject } from "@app/pages/Project";

import ContentHeader from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content/ContentHeader";
import { ContentInputForm } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm";

import { BUTTON, CONSTANT } from "@constant";
import { getToolInfo } from "@app/pages/Project/ProjectDetail/projectCommons";

import { streamSseMarkExam, submitMark } from "@services/Content";
import { cloneObj } from "@common/functionCommons";
import { convertArrayToObject, extractKeys } from "@common/dataConverter";

import "./MarkExamContent.scss";
import MarkStudentOutput from "./MarkStudentOutput";
import AntButton from "@src/app/component/AntButton";
import { createInput, updateInputMarkTest } from "@src/app/services/Input";
import AssignmentInput from "./AssignmentInput";

export const MarkStudentContext = React.createContext();

function MarkStudentContent() {
  const { projectId, projectContentData, subSelectedIndex } = useProject();
  const { projectData, setProjectContentData } = useProject();
  const { t, i18n } = useTranslation();

  const projectCommonOptions = useRef({});
  const isStreaming = useRef(false);
  const eventSource = useRef(undefined);

  const [formEssay] = Form.useForm();

  const [instructionSelected, setInstructionSelected] = useState(null);
  const [examSubData, setExamSubData] = useState([]);
  const [isMarking, setMarking] = useState(false);

  const [fileData, setFileData] = useState({});

  const subSelectedData = useMemo(() => examSubData?.[subSelectedIndex], [examSubData, subSelectedIndex]);

  const contentData = useMemo(() => {
    if (!projectContentData?.length) return null;
    return projectContentData[0];
  }, [projectContentData]);

  const isExistProcessing = useMemo(() => {
    return contentData?.responses?.some(response => {
      return response.state?.toUpperCase() === CONSTANT.PROCESSING
    })
  }, [contentData?.responses]);

  const { inputData } = contentData?.inputs?.[0] || {};

  useEffect(() => {
    const { text, fileId, startPage, endPage, totalPages } = inputData || {};
    setFileData({ fileId, startPage, endPage, totalPages })
    formEssay.setFieldsValue({ text: text });
  }, [inputData]);

  useEffect(() => {
    return () => closeEv();
  }, []);

  useEffect(() => {
    projectCommonOptions.current = projectData?.commonOptions;
  }, [projectData?.commonOptions]);

  useEffect(() => {
    handleStreamSubmission();
  }, [isExistProcessing]);

  function handleStreamSubmission() {
    if (!isExistProcessing) {
      if (isStreaming.current) {
        closeEv();
        isStreaming.current = false
      }
      return;
    }
    isStreaming.current = true;
    eventSource.current = streamSseMarkExam(projectId);
    eventSource.current.addEventListener(projectId, async (event) => {
      const data = JSON.parse(event?.data);

      if (data?.state?.toUpperCase() === CONSTANT.DONE) {
        const inputGrouped = convertArrayToObject(extractKeys(data.responses, "inputId"), "_id");
        const responseGrouped = convertArrayToObject(data.responses, "_id");

        setProjectContentData(prevState => {
          const newState = cloneObj(prevState);
          newState[0].inputs = newState[0].inputs?.map(input => inputGrouped[input?._id] || input);
          newState[0].responses = newState[0].responses?.map(response => {
            if (responseGrouped[response?._id]) {
              const newResponse = responseGrouped[response?._id];
              return {
                ...newResponse,
                typingEffect: subSelectedData?._id === response?.inputId?._id && !newResponse?.canceledSubmit,
              };
            }
            return response;
          });
          return newState;
        });
        isStreaming.current = false;
      }
    });
    eventSource.current.addEventListener("error", async (error) => {
      closeEv();
    });
  }

  function closeEv() {
    eventSource.current?.close();
    eventSource.current = null;
  }

  const toolInfo = useMemo(() => getToolInfo(contentData), [contentData, i18n.language]);

  const createNewInput = async (values) => {
    const dataRequest = {
      contentId: contentData._id,
      toolId: toolInfo._id,
      inputData: {
        markTestType: 'text',
        ...values?.inputData
      },
      inputType: toolInfo.inputType,
    };

    const inputResponse = await createInput(dataRequest);
    return inputResponse;
  };

  const updateInput = async (inputId, values) => {
    const dataRequest = {
      _id: inputId,
      inputData: {
        markTestType: 'text',
        ...values?.inputData
      },
    };

    const inputResponse = await updateInputMarkTest(dataRequest);
    return inputResponse;
  }

  async function onFinish(values) {
    const essayText = formEssay.getFieldValue('text');
    if (!essayText && !fileData?.fileId) {
      return;
    }

    const dataRequest = {
      ...values,
      inputData: {
        ...values.inputData,
        ...essayText ? {
          text: essayText,
          markTestType: "text",
        } : {},
        ...fileData?.fileId ? {
          ...fileData,
          markTestType: 'file',
        } : {},
      }
    }
    const { inputs } = contentData || {};
    const isCreate = !inputs?.length;
    setMarking(true);
    const inputResponse = isCreate ? await createNewInput({ ...dataRequest }) : await updateInput(inputs[0]._id, dataRequest);

    if (inputResponse) {
      const apiRequest = {
        projectId,
        workspaceId: projectData.workspaceId,
        contentId: contentData._id,
        inputIds: [inputResponse._id]
        //commonOptions: projectData?.commonOptions,
      };

      const apiResponse = await submitMark(apiRequest);
      if (Array.isArray(apiResponse)) {
        // const typingEffectResponse = apiResponse.map(response => {
        //   response.activate = true;
        //   return response;
        // });

        setProjectContentData(prevState => {
          const newState = cloneObj(prevState);
          newState[0].inputs = [apiResponse[0].inputId];

          newState[0].responses ||= [];
          newState[0].responses = apiResponse;
          return newState;
        });
      } else if (apiResponse?.success && apiResponse?.message) {
        toast.warning({ description: apiResponse.message });
      }
      setMarking(false);
    }

  }

  if (!projectContentData.length) return null;

  return <MarkStudentContext.Provider value={{
    examSubData, setExamSubData,
    subSelectedData,
    contentData,
    // resultData, 

    //
    isMarking, setMarking,
    fileData, setFileData,
    formEssay
  }}>

    <div className="mark-exam-content">
      <ContentHeader toolInfo={toolInfo} />

      <ContentInputForm
        formKey={CONSTANT.MARK_STUDENT}
        content={contentData}
        inputData={contentData?.inputs?.[0]?.inputData || {}}

        toolInfo={toolInfo}
        instructionSelected={instructionSelected}
        setInstructionSelected={setInstructionSelected}
        isDisabledContent={false}

        onFinish={onFinish}
      />
      <AssignmentInput
        instructionId={instructionSelected}
        toolInfo={toolInfo}
      />
      <div className="mark-exam__submit">
        <AntButton
          size="large"
          type={BUTTON.DEEP_NAVY}
          disabled={isMarking || isExistProcessing}
          loading={isMarking}

          htmlType="submit"
          form={`${CONSTANT.INSTRUCTION}-${contentData._id}${CONSTANT.MARK_STUDENT}`}
          onClick={() => formEssay.validateFields(['text'])}
        >
          {t("MARK")}
        </AntButton>
      </div>
      <MarkStudentOutput />
    </div>
  </MarkStudentContext.Provider>;
}

const useMarkStudent = () => useContext(MarkStudentContext);

export { MarkStudentContent, useMarkStudent };