.exam-project-input-container {
  .exam-project-input__body {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .exam-project-input__body-submit {
      display: flex;
      align-items: center;
      flex-direction: column;
      gap: 16px;
    }
  }

  .exam-input__add-tool {
    display: flex;
    justify-content: center;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      width: 100%;
      left: 0;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      border-top: 1px dashed var(--support-colours-grey-light);
    }
  }
}