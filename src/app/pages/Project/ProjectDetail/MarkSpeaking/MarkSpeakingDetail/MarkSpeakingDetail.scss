.mark-speaking-container {
  background: var(--white);
  padding: 16px 24px;

  .speaking-recorder-container {
    min-height: 300px;
    padding: 30px;
    background-image: linear-gradient(rgb(255, 255, 255), rgba(135, 180, 235, 0.3));
    border: 1px solid #d1d1d1;
    border-radius: 8px;
    justify-items: center;

    .speaking-recorder__form {
      width: 400px;

      .ant-form-item-control-input-content {
        font-size: 24px;
        font-weight: 600;
        width: 400px;
        border-radius: 8px;
        overflow: hidden;

        > * {
          border-radius: 8px;
          border-color: #424242;
          border-style: dotted;
          border-width: 1px;


        }

        .ant-input-outlined.ant-input-disabled, .ant-input-outlined[disabled],
        .ant-select-outlined.ant-select-disabled:not(.ant-select-customize-input) .ant-select-selector {
          color: #000;
        }

        .ant-select-selector {
          border-radius: 8px;
        }
      }
    }

    .speaking-recorder__description {
      font-size: 14px;
      margin-top: 10px;
      margin-bottom: 32px;
    }

  }

  .speaking-response {
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;

    border: 1px solid #d1d1d1;
    display: flex;
    flex-direction: row;

    .speaking-response__left {
      width: 955px;


      .speaking-response__transcript {
        padding: 8px 20px 16px 20px;

        .speaking-response__transcript-title {
          line-height: 56px;
          font-size: 18px;
          font-weight: 600;
          font-style: italic;
          color: #242424;
        }

        .speaking-response__transcript-content {
          user-select: none;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          align-items: center;

          > span:not(.transcript__monotone) {
            display: inline-block;
          }

          .transcript__monotone, .transcript__word-monotone {
            background: transparent;
            //background: rgba(0, 255, 255, .2);
            position: relative;
            display: flex;
            flex-wrap: wrap;

            .transcript__monotone-bg {

            }

            &::before, &::after {
              content: '';
              position: absolute;
              bottom: 5px;
              left: 0;
              right: 0;
              height: 6px;
            }

            &::before {
              background: linear-gradient(135deg, transparent 40%, rgb(134, 46, 181) 50%, transparent 60%) 9px 0px / 12px 6px repeat-x
            }

            &::after {
              background: linear-gradient(45deg, transparent 40%, rgb(134, 46, 181) 50%, transparent 60%) 3px 0px / 12px 6px repeat-x
            }
          }

          .transcript__word {
            font-size: 18px;
            line-height: 53px;
            padding: 1px 3px;
            position: relative;

            .transcript__word-mispronunciation {
              background-color: rgb(255, 204, 0);
              border-radius: 4px;
              text-decoration-line: underline;
            }
          }

          .transcript__missing-break {
            margin: 1px 3px;
            font-size: 18px;
            height: 24px;
            background-color: rgb(212, 212, 212);
            border-radius: 4px;
          }

          .transcript__unexpected-break {
            margin: 1px 3px;
            font-size: 18px;
            height: 24px;
            background-color: rgb(240, 201, 203);
            border-radius: 4px;
            text-decoration-line: line-through;
          }

          .ant-popover .ant-popover-inner {
            padding: 5px 12px;
            font-size: 12px;

            .ant-popover-title {
              min-width: unset;
              line-height: 16px;
              margin: 0;
              font-weight: 400;
            }

            .ant-popover-inner-content {
              .word-phonemes {
                display: flex;
                flex-direction: row;
                gap: 12px;

                .word-phonemes__phoneme {
                  display: flex;
                  flex-direction: column;

                  > span {
                    line-height: 16px;
                  }
                }
              }
            }
          }
        }
      }
    }

    .speaking-response__error {
      border-left: 1px solid #d1d1d1;
      padding: 0 16px 16px 16px;
      flex: 1;

      .speaking-response__error-title {
        line-height: 43px;
        font-weight: 600;
      }

      .speaking-response__error-content {
        display: flex;
        flex-direction: column;
        row-gap: 24px;

        .speaking-response__error-item {
          display: flex;
          flex-direction: row;

          .speaking-error__count {
            width: 20px;
            line-height: 20px;
            text-align: center;
            font-size: 12px;
            border-radius: 4px;
          }

          .speaking-error__title {
            margin-left: 6px;
            font-size: 14px;
            line-height: 20px;
          }

          .speaking-error__switch {
            margin-left: auto;
            padding-left: 18px;
            display: flex;
            gap: 9px;

            .speaking-error__switch-text {
              font-size: 14px;
              width: 20px;
            }
          }
        }
      }
    }
  }

  .speaking-score {
    display: flex;
    flex-direction: row;
    gap: 20px;
    border: 1px solid #d1d1d1;
    border-top-width: 0;
    padding: 10px 20px;
    border-bottom-right-radius: 8px;
    border-bottom-left-radius: 8px;
    flex-wrap: wrap;
  }

  .open-ai-result {
    margin-top: 16px;
  }

  .speaking-assessment-result {
    margin: 16px 0 6px 0;
    font-size: 14px;
    font-weight: 600;

  }
}