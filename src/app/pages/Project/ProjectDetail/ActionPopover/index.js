import React from "react";
import { Popover } from "antd";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";

import "./ActionPopover.scss";

function ActionPopover({ content, ...props }) {
  const { t } = useTranslation();
  
  return <Popover
    placement="topLeft"
    content={t(content)}
    rootClassName="action-popover-container"
  >
    <AntButton className="action-popover-btn" size="large" {...props} />
  </Popover>;
}

export default ActionPopover;