.select-resource-video {
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media screen and (max-height: 720px) {
    gap: 16px;
  }

  .dropzone-inner {
    padding: 80px 20px !important;
  }

  .select-resource-content {

    .select-resource__list {
      display: grid;
      grid-template-columns: repeat(4, minmax(0, 1fr));
      gap: 24px;
      max-height: calc(100vh - 490px);
      overflow-y: auto;

      @media screen and (max-height: 720px) {
        max-height: calc(100vh - 330px);

        column-gap: 24px;
        row-gap: 16px;
      }

      .select-resource__item {
        cursor: pointer;
        padding: 15px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        background-color: var(--white);
        border-radius: 8px;
        border: 1px solid transparent;

        &.select-resource__item-active {
          border-color: var(--primary-colours-blue-navy);
        }


        .resource-item__video-embed {
          display: flex;
          aspect-ratio: 16 / 9;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            user-select: none;
          }
        }

        .resource-item__youtube-icon {
          margin-bottom: -2px;
          margin-right: 4px;
        }

        .resource-item__update-at {
          font-size: 10px;
          color: var(--typo-colours-support-blue-light);
        }
      }
    }
  }

}