import React, { useEffect, useState } from "react";
import { Form } from "antd";
import { useTranslation } from "react-i18next";

import { useContentInput } from "@app/pages/Project/ProjectDetail/ContentInput/ContentInputForm";
import { AntForm } from "@component/AntForm";
import CustomCKEditor from "@src/app/component/CKEditor";

import RULE from "@rule";
import { CONSTANT } from "@constant";

import "./Html.scss";

function Html(props) {
  const { t } = useTranslation();
  
  const { formKey } = useContentInput();
  const { isDisabledContent, content, inputData, toolInfo } = props;
  
  const [maxWords, setMaxWords] = useState(1000);
  const [wordCount, setWordCount] = useState(0);
  
  const [htmlForm] = Form.useForm();
  
  useEffect(() => {
    setMaxWords(toolInfo?.maxInputLength || 1000);
  }, [toolInfo]);
  
  useEffect(() => {
    if (inputData) {
      setDataForm(inputData);
    }
  }, [inputData?.text]);
  
  function setDataForm(values) {
    htmlForm.setFieldsValue(values);
    props.onChange(values);
  }
  
  return <>
    <AntForm
      id={`${CONSTANT.SAMPLE_EXAM}-${content?._id}${formKey}`}
      name={`${CONSTANT.SAMPLE_EXAM}-${content?._id}${formKey}`}
      className="form-html-input"
      layout="vertical"
      form={htmlForm}
      disabled={isDisabledContent}
    >
      <AntForm.Item
        className="form-html-input__html"
        name="text"
        label={toolInfo?.inputLabel || t("SAMPLE_EXAM")}
        rules={[
          RULE.REQUIRED,
          {
            validator: () => {
              if (!wordCount) return Promise.resolve();
              
              if (wordCount > maxWords) {
                return Promise.reject(new Error(t("ONLY_ENTER_UP_TO_NUMBER_WORD").format(maxWords)));
              }
              
              return Promise.resolve();
            },
          }]}
      >
        <CustomCKEditor
          placeholder={toolInfo?.inputPlaceholder || t("ENTER_SAMPLE_EXAM")}
          onCountWord={setWordCount}
          count={{ show: true, max: maxWords }}
        />
      </AntForm.Item>
    </AntForm>
  </>;
}


export default Html;