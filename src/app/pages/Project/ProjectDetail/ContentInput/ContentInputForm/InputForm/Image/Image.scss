.form-image-input {
  display: none;
}

.image-resource-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;

  .image-resource-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 16px;

    .image-resource__title {
      font-weight: 600;
    }

    .image-resource-action {
      margin-top: -2px;
      margin-bottom: -2px;
    }
  }

  .resource-image-name {
    cursor: default;
    background-color: unset;
    color: var(--black);
  }

  .upload-image__preview {
    display: flex;
    background-color: var(--blue-light-2);

    img {
      user-select: none;
      width: 100%;
      aspect-ratio: 2.2 / 1;
      object-fit: contain;
    }

    .image-preview__icon {
      svg {
        width: 24px;
        height: 24px;
      }
    }
  }
}