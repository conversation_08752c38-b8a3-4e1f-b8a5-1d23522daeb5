.writing-upload-file-modal {
  @media screen and (max-width: 767.98px) {
    width: 100% !important;
  }

  @media screen and (min-width: 768px) and (max-width: 1023.98px) {
    width: 90% !important;
  }

  @media screen and (min-width: 1024px) and (max-width: 1535.98px) {
    width: 70% !important;
  }

  @media screen and (min-width: 1536px) {
    width: 58% !important;
  }

  .modal-content {
    padding: 16px;
    font-family: Inter, serif;

    .ant-modal-close {
      width: 32px;
      height: 32px;
      top: 16px;
      inset-inline-end: 16px;
    }

    .modal-header .ant-modal-title .ant-modal-title__text {
      font-size: 22px;
      font-weight: 600;
      line-height: 30px;
    }

    .modal-header {
      margin-bottom: 8px;
    }
  }

  .modal-body {
    >.loading-component {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .notice-images {
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        text-align: left;
        color: var(--primary-colours-blue-navy);
      }

      .preview-file-title {
        font-weight: 500;
        line-height: 24px;
        text-align: left;
        color: var(--primary-colours-blue-navy);
      }
    }
  }



  .footer-action {
    justify-content: center;
    display: flex;
    gap: 24px;
    margin-top: 8px;

    button {
      font-family: Inter, serif;
      gap: 16px;
      border-radius: 12px;
      padding: 8px 16px 8px 16px;
      gap: 10px;
    }
  }
}