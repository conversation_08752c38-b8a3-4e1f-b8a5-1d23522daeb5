.preview-image-essay {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;

  .ant-image {
    height: 250px;
    border: 1px solid #DBDBDB;
    border-radius: 8px;

    .ant-image-mask {
      border-radius: 8px;
      background: #00000066;

      .image-preview-icon {
        svg {
          width: 24px;
          height: 24px;
        }
      }
    }

    .preview-image-essay__image {
      height: 100%;
      border-radius: 8px;

      &.image-hidden {
        display: none;
      }
    }

    &:has(.image-hidden) {
      display: none;
    }

    &:has(.image-result) {
      height: 100px;

      .image-preview-icon {
        svg {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .select-image {
    display: flex;
    gap: 16px;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: var(--primary-colours-blue-navy);
    align-items: center;

    .ant-btn.ant-btn-mini {
      height: 16px;
      width: 16px;
      min-width: 16px;
    }

  }
}