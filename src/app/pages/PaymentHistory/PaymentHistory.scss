.payment-history-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  border-radius: 8px;
  background-color: var(--backgroun-1st);

  .title__icon {
    box-shadow: 0px 4px 10px 0px #0000001A;
    background-color: var(--background-light-background-2);
    padding: 8px;
    border-radius: 8px;
    display: flex;

    img {
      width: 16px;
      height: 16px;
    }
  }

  .subcription-info {
    display: flex;
    padding: 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border-radius: 16px;
    color: #FFF;
    background-color: var(--primary-colours-blue);

    .subcription-info__title {
      display: flex;
      gap: 8px;
      font-weight: 600;
      align-items: center;
    }

    .subcription-info__item {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .item__label {
        display: flex;
        gap: 8px;
      }

      .package__info {
        font-size: 22px;
        font-weight: 700;
      }

      .subcription-time {
        display: flex;
        gap: 80px;
        font-size: 14px;

        .subcription-time__label {
          &::after {
            content: ' '
          }
        }
      }
    }
  }

  .transaction-history {
    display: flex;
    gap: 24px;
    align-items: flex-start;

    .transaction-list {
      display: flex;
      flex-direction: column;
      gap: 24px;
      width: 100%;

      .transaction-list__title {
        display: flex;
        gap: 8px;
        align-items: center;
        font-weight: 600;
      }

      .ant-table {
        border-radius: 8px;
      }

      .ant-table-thead {
        position: relative;
        gap: 4px;


        th::before {
          display: none;
        }

        th:first-child {
          padding: 18px 16px 18px 24px;
        }

        th:last-child {
          padding: 18px 24px 18px 16px;
        }

        .ant-table-cell {
          background: unset;
          padding: 18px 16px;
        }
      }

      .ant-table-row {
        cursor: pointer;

        &:hover {
          .ant-table-cell {
            background: unset !important;
          }
        }

        &.transaction-selected {
          .ant-table-cell {
            background: var(--primary-colours-blue-light-1) !important;
          }
        }

        td:first-child {
          padding: 14px 16px 14px 24px;
        }

        td:last-child {
          padding: 14px 24px 14px 16px;
        }

        .ant-table-cell {
          background: unset;
          padding: 14px 16px;

          .package-info {
            display: flex;
            gap: 8px;
            align-items: center;
          }
        }

      }

      .transaction-status-done {
        color: var(--typo-colours-support-blue)
      }

      .transaction-status-error {
        color: var(--support-colours-red-dark)
      }

      .transaction-status-cancel {
        color: var(--typo-colours-support-yellow)
      }

      .transaction-status-processing {
        color: var(--support-colours-green-dark)
      }
    }

    .transaction-detail {
      display: flex;
      flex-direction: column;
      gap: 40px;
      padding: 24px;
      border-radius: 16px;
      box-shadow: 0px 4px 20px 0px #0000001A;
      max-width: 360px;

      .transaction-detail__title {
        font-size: 24px;
        font-weight: 600;
      }

      .transaction-detail__content {
        display: flex;
        flex-direction: column;
        gap: 24px;

        .transaction-detail__content__item {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .content__item__title {
            font-weight: 600;
          }

          .package-info {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .package-info__name {
              display: flex;
              gap: 8px;
              align-items: center;
            }
          }

          .payment-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 0 16px;

            .payment-info__item {
              display: flex;
              justify-content: space-between;

              &.payment-info__discount {
                padding-left: 16px;
                color: var(--typo-colours-support-blue-light);

                .payment-info__item__label {
                  font-weight: 400;
                }
              }
            }
          }

          .hotline-support {
            color: var(--typo-colours-support-blue);
          }
        }
      }
    }
  }

  .package-info__unit-price {
    padding: 4px 10px;
    border-radius: 4px;
    background-color: var(--primary-colours-blue-light-2);
    color: var(--typo-colours-support-blue);
  }
}