import React, { useState } from "react";
import { connect } from "react-redux";
import { Form, Table, Tooltip } from "antd";
import { useTranslation } from "react-i18next";

import { CONSTANT, INVITATION_STATUS } from "@constant";

import ManagerUserAction from "@src/app/pages/Organization/OrganizationInfo/ManageUser/ManagerUserAction";
import ModalManagerUser from "@src/app/pages/Organization/OrganizationInfo/ManageUser/ModalManagerUser";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import { deleteMember, resendInvitationMember, updateInvitationMember } from "@src/app/services/Invitation";

import RESEND_INVITATION from "@src/asset/icon/organization/resend-invitation-icon.svg";

import "./ManageUser.scss";

ManageUser.propTypes = {};

function ManageUser({ user, allMembers, membersDisplay, setAllMembers }) {
  const [idModalEditUser, setIdModalEditUser] = useState(null);
  const [formModalEditUser] = Form.useForm();
  const { t } = useTranslation();

  const handleOpenModalEditMember = (userData) => {
    setIdModalEditUser(userData?._id);
    formModalEditUser.setFieldsValue(userData);
  };

  const handleCloseModalEditUser = () => {
    setIdModalEditUser(null);
    formModalEditUser.resetFields();
  };
  const handleResendInvitation = async (record) => {
    const { email, organizationId } = record;
    const apiResponse = await resendInvitationMember({ email, organizationId });
    if (apiResponse) {
      const temp = allMembers?.filter((data) => data?._id !== apiResponse?._id);
      const updatedRows = [apiResponse, ...temp];
      setAllMembers(updatedRows);
      toast.success("INVITATION_RESENT");
    }
  };
  const handleDeleteMember = async (idUser) => {
    confirm.delete({
      content: t("CONFIRM_DELETE_USER"),
      handleConfirm: async (e) => {
        const apiResponse = await deleteMember(idUser);
        if (apiResponse) {
          toast.success("DELETE_MEMBER_SUCCESS");
          const newDataUserOrganization = membersDisplay?.filter((item) => item?._id !== apiResponse?._id);
          setAllMembers(newDataUserOrganization);
        }
      },
    });
  };
  const columns = [
    {
      title: t("FULL_NAME"),
      dataIndex: "fullName",
      key: "fullName",
      width: 200,
    },
    {
      title: t("EMAIL"),
      dataIndex: "email",
      key: "email",
      width: 200,
      render: (_, record) => <span className="manager-user-rows">{record?.email}</span>,
    },
    {
      title: t("ROLE"),
      dataIndex: "role",
      key: "role",

      render: (_, record) => (
        <span className="manager-user-rows">
          {record?.role === CONSTANT.ADMIN && t("ORGANIZATION_MANAGER")}
          {record?.role === CONSTANT.NORMAL && t("MEMBER")}
          {record?.role === CONSTANT.CONTRIBUTOR && t("CURRICULUM_MANAGER")}
        </span>
      ),
      width: 150,
    },
    {
      title: t("STATUS"),
      dataIndex: "status",
      key: "status",
      render: (value, record) => {
        const isAccepted = record?.status === INVITATION_STATUS["accepted"].value;
        return <div className="invitation-pending">
          <span>{t(INVITATION_STATUS[value]?.lang)}</span>
          {!isAccepted && <Tooltip
            title={t("RESEND_INVITATION")}
            className="tooltip-resend-invitation"
            placement="bottomLeft"
            color={"#828FA2"}
            onClick={() => handleResendInvitation(record)}
          >
            <img src={RESEND_INVITATION} />
          </Tooltip>}
        </div>
      },
      width: 100,
    },
  ];
  const actionsColumn = {
    title: t("ACTION"),
    key: "action",
    width: 50,
    align: "center",
    show: false,
    render: (_, record) => (
      <div className={"manager-organization-user-actions"}>
        {user?.email !== record?.email && (
          <ManagerUserAction
            menuClassName={"dropdown-actions-manager-user"}
            onDeleteAction={() => handleDeleteMember(record?._id)}
            onEditAction={() => handleOpenModalEditMember(record)}
            disableDelete={record?.status === INVITATION_STATUS.pending.value}
          />
        )}
      </div>
    ),
  };
  if (user?.role === CONSTANT.ADMIN) {
    columns.push(actionsColumn);
  }

  const submitFormEditUser = async (userEditData) => {
    const { role } = userEditData;
    const apiResponse = await updateInvitationMember({ _id: idModalEditUser, role });
    if (apiResponse) {
      toast.success("UPDATE_MEMBER_SUCCESS");
      handleCloseModalEditUser();
      const newDataUserOrganization = membersDisplay?.map((member) =>
        member?._id === apiResponse?._id ? apiResponse : member,
      );
      setAllMembers(newDataUserOrganization);
    }
  };

  const pagination = {
    total: membersDisplay?.length,
    pageSize: 10
  }

  return (
    <div className="manager-organization-user">
      <Table
        scroll={{ x: "max-content" }}
        dataSource={membersDisplay}
        pagination={pagination}
        className={"table-manager-user"}
        columns={columns}
      />
      <ModalManagerUser
        form={formModalEditUser}
        isShowModal={idModalEditUser}
        handleOk={submitFormEditUser}
        handleCancel={handleCloseModalEditUser}
        titleOk={t("UPDATE")}
        title={t("EDIT_MEMBER")}
        formId="form-edit-user-organization"
        isEdit={true}
      ></ModalManagerUser>
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(ManageUser);
