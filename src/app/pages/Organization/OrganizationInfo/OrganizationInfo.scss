.organization-info {
  display: flex;
  flex-direction: row;
  gap: 24px;
  background-color: var(--background-light-background-2);
  padding: 24px;
  border-radius: 8px;
  min-height: 100%;

  .organization-content {
    display: flex;
    flex-direction: column;
    width: calc(100% - 360px - 24px);
    gap: 24px;

    .organization-content-rows {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 8px;

      .organization-content-title {
        font-size: 16px;
        line-height: 20px;
        font-weight: 600;
      }

      .organization-content-icon {
        box-shadow: var(--shadow-level-1);
        border-radius: 8px;
      }
    }

    .form-organization-info {
      width: 100%;

      .ant-form-item {
        flex: auto;
      }

      label {
        font-weight: 400 !important;
        font-size: 16px !important;
        line-height: 20px !important;
      }

      .btn-cancel-edit-organization {
        height: 40px;
        margin-right: 8px;
        margin-left: -8px;
        border-radius: 8px;
      }
    }

    .organization-content__total-member {
      color: var(--typo-colours-support-green);

      &::before {
        content: ' ';
      }
    }

    .gap-16 {
      gap: 16px !important;
    }
  }

  .organization-detail {
    padding: 24px;
    border-radius: 16px;
    width: 360px;
    gap: 24px;
    box-shadow: var(--shadow-level-2);
    display: flex;
    flex-direction: column;

    .updload-avartar-loading {
      display: flex;
      justify-content: center;

      .loading-spin {
        top: 50%;
      }

      .organization-detail__avatar {
        display: flex;
        justify-content: center;
        align-self: center;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 1px solid var(--support-colours-grey-light);
        cursor: pointer;
        position: relative;
        overflow: hidden;

        img {
          display: flex;
          align-self: center;
          max-width: 100%;
          max-height: 100%;
        }

        &::before {
          position: absolute;
          bottom: -100%;
          left: 0;
          height: 50%;
          width: 100%;
          background-color: #000000CC;
          background-image: url('/src/asset/icon/camera/camera.svg');
          background-repeat: no-repeat;
          background-position: center;
          content: "";
          transition: bottom 0.3s ease-in-out;
        }

        &:hover::before{
          bottom: 0;
        }
      }
    }

    .organization-detail__name {
      font-size: 24px;
      font-weight: 600;
      line-height: 30px;
      text-align: center;
    }

    .organization-detail__info {
      display: flex;
      flex-direction: column;
      gap: 24px;

      .organization-detail__info-item {
        display: flex;
        flex-direction: column;
        line-height: 20px;
        text-align: left;

        .organization-detail__info-item-label {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }

    .organization-detail__chart {
      display: flex;
      flex-direction: column;
      gap: 22px;
      padding: 24px;
      border-radius: 8px;
      box-shadow: var(--shadow-level-1);

      .organization-detail__chart-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .chart-header__item {
          display: flex;
          flex-direction: column;

          &.chart-header__item-left-side {
            text-align: left;
          }

          &.chart-header__item-right-side {
            text-align: right;
          }

          .chart-header__item-title {
            font-weight: 600;
            font-size: 16px;
          }

          .chart-header__item-value {
            font-weight: 400;
            font-size: 14px;
            color: var(--typo-colours-support-blue-light);

            &.out-of-date {
              color: var(--support-colours-red);
            }
          }
        }
      }

      .organization-detail__chart-footer {
        font-size: 16px;
        text-align: center;
      }
    }
  }
}

.modal-add-new-user-organization {
  label {
    height: auto !important;
  }

  .modal-add-new-user-organization__title {
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    color: var(--typo-colours-support-blue);
  }
}