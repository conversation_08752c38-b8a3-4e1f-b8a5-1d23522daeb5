import React, { useContext, useMemo, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Tabs } from "antd";

import { usePageViewTracker } from "@src/ga";

import NeedAccess from "@component/NeedAccess";
import OrganizationInfo from "./OrganizationInfo";
import MemberActivities from "./MemberActivities";
import TrackingMember from "./TrackingMember";
import { OrganizationTemplate } from "./OrganizationTemplate";

import { CONSTANT, ORG_TABS } from "@constant";

import "./Organization.scss";

export const OrganizationContext = React.createContext();

function Organization({ user, ...props }) {
  const { t } = useTranslation();
  usePageViewTracker("Organization");
  
  const [activeTab, setActiveTab] = useState(ORG_TABS.ORGANIZATION);
  const [allMembers, setAllMembers] = useState([]);
  
  const orgId = useMemo(() => user?.organizationId?._id, [user]);
  if (user?.role !== CONSTANT.ADMIN) return <NeedAccess />;
  
  return (<OrganizationContext.Provider value={{
      allMembers, setAllMembers,
      activeTab,
      orgId,
    }}>
      <div className="organization-container">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="organization-tabs"
          items={[
            {
              key: ORG_TABS.ORGANIZATION,
              label: t("ORGANIZATION"),
              children: <OrganizationInfo />,
            },
            {
              key: ORG_TABS.ACTIVITY,
              label: t("MEMBERS_ACTIVITIES"),
              children: <MemberActivities />,
            },
            {
              key: ORG_TABS.TRACKING,
              label: t("TRACKING_MEMBER"),
              children: <TrackingMember />,
            },
            {
              key: ORG_TABS.TEMPLATE,
              label: t("TEMPLATE"),
              children: <OrganizationTemplate />,
            },
          ]}
        />
        {/*<OrganizationInfo />*/}
        {/*<MemberActivities />*/}
        {/*<OrganizationTemplate />*/}
      
      
      </div>
    </OrganizationContext.Provider>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const ConnectedOrganization = connect(mapStateToProps)(Organization);


const useOrganization = () => useContext(OrganizationContext);

export { ConnectedOrganization as Organization, useOrganization };
