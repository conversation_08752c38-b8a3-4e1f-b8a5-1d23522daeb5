import React from "react";
import { useTranslation } from "react-i18next";
import clsx from "clsx";


import UploadIcon from "@component/SvgIcons/Upload";

import { API } from "@api";
import { formatTimeDate } from "@common/functionCommons";

import CLICKEE_AVATAR from "@src/asset/logo/clickee-avatar.svg";

import "./OrgDocTemplateList.scss";
import OrgDocTemplateItem from "@app/pages/Organization/OrganizationTemplate/OrgDocTemplateList/OrgDocTemplateItem";


function OrgDocTemplateList({ dataSource, onUpload, itemSelected, onSelectItem, publicField = "", isGridLayout = false }) {
  const { t } = useTranslation();
  
  return <>
    <div className={clsx("org-doc-template-list", { "org-doc-template-list__grid": isGridLayout })}>
      {!!onUpload && <div
        className="org-doc-template-item org-doc-template-item__upload"
        onClick={onUpload}
      >
        <UploadIcon />
        {t("UPLOAD")}
      </div>}
      {dataSource?.map(sourceItem => {
        return <OrgDocTemplateItem
          key={sourceItem._id}
          sourceItem={sourceItem}
          itemSelected={itemSelected}
          onSelectItem={onSelectItem}
          publicField={publicField}
        />;
      })}
    </div>
  </>;
}

export default OrgDocTemplateList;