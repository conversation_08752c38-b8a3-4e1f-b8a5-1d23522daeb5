import React, { useEffect, useState } from "react";
import { Col, DatePicker, Form, Input, InputNumber, Row, Select, Switch } from "antd";
import { useTranslation } from "react-i18next";

import AntModal from "@src/app/component/AntModal";

import { PROMOTION_TYPE } from "@constant";
import { AntForm } from "@src/app/component/AntForm";
import RULE from "@rule";
import dayjs from "dayjs";


const PromotionDetailModal = ({ ...props }) => {
  const { open, promotion, allPackage, onCancel, onFinish } = props;
  const [form] = Form.useForm();
  const { t } = useTranslation();

  const [type, setType] = useState(null);
  const [prices, setPrices] = useState([]);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);

  useEffect(() => {
    if (promotion) {
      const { startDate, endDate } = promotion;
      const dayjsStartDate = startDate ? dayjs(startDate) : null;
      const dayjsEndDate = endDate ? dayjs(endDate) : null;
      setMaxDate(dayjsEndDate);
      setMinDate(dayjsStartDate);
      form.setFieldsValue({ ...promotion, packageId: promotion.packageId?._id, startDate: dayjsStartDate, endDate: dayjsEndDate });
    } else {
      setPrices([]);
      setType(null);
    }
  }, [promotion]);

  useEffect(() => {
    if (!open) {
      form.resetFields();
      setPrices([]);
    }
  }, [open]);

  useEffect(() => {
    if (promotion && allPackage.length) {
      const newPrices = allPackage.find(item => item._id === promotion.packageId?._id)?.prices?.map((price, index) => {
        return {
          label: `${price?.intervalCount} - ${price?.unitName} - ${price?.unitAmount}`,
          value: index + 1
        }
      }) || [];
      setPrices(newPrices);
    }
  }, [promotion, allPackage]);

  useEffect(() => {

  }, [promotion]);

  const handleChangeType = (value) => {
    setType(value);
    form.setFieldsValue({ discount: null });
  }

  const handleChangePackage = (_, option) => {
    const newPrices = option.prices.map((price, index) => {
      return {
        label: `${price?.intervalCount} - ${price?.unitName} - ${price?.unitAmount}`,
        value: index + 1
      }
    })
    setPrices(newPrices);
    form.setFieldsValue({ priceIndex: 1 });
  }

  return (
    <AntModal
      open={open}
      onCancel={() => onCancel()}
      title={promotion ? t("UPDATE_PROMOTION") : t("CREATE_PROMOTION")}
      width={1000}
      className="knowledge-modal"
      okText={promotion ? t("UPDATE") : t("CREATE")}
      formId="form-promotion"
    >
      <AntForm onFinish={onFinish} layout="vertical" form={form} id="form-promotion" size={"large"}>
        <Row gutter={24}>
          <Col xs={12} >
            <AntForm.Item label={t("DISPLAY_TEXT")} name="displayText" rules={[RULE.REQUIRED]}>
              <Input placeholder={t("DISPLAY_TEXT")} />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item label={t("DESCRIPTION")} name="description">
              <Input placeholder={t("DESCRIPTION")} />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item label={t("TYPE")} name="type" rules={[RULE.REQUIRED]}>
              <Select
                placeholder={t("SELECT_TYPE")}
                options={Object.values(PROMOTION_TYPE).map(({ value, lang }) => ({ value: value, label: t(lang) }))}
                onChange={handleChangeType}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item label={t("PROMOTION")} name="discount">
              <InputNumber min={1} placeholder={t("PROMOTION")} controls={false}
                {...type === PROMOTION_TYPE.PERCENTAGE.value ? { max: 100 } : {}}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item label={t("PACKAGE")} name="packageId" rules={[RULE.REQUIRED]}>
              <Select
                placeholder={t("SELECT_PACKAGE")}
                options={allPackage}
                fieldNames={{ label: "name", value: "_id" }}
                onChange={handleChangePackage}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item
              label={t("PERIOD")}
              name="priceIndex"
              {...prices?.length ? {} : { hidden: true }}
            >
              <Select
                placeholder={t("SELECT_PERIOD")}
                options={prices}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item
              name={"startDate"}
              rules={[
                () => ({
                  validator(_, value) {
                    if (!!value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                  },
                }),
              ]}
            >
              <DatePicker
                placeholder={t("SELECT_START_DATE")}
                size="large"
                style={{ width: '100%', padding: '9px 11px' }}
                format="DD/MM/YYYY"
                maxDate={maxDate}
                onChange={setMinDate}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item
              name={"endDate"}
              rules={[
                () => ({
                  validator(_, value) {
                    if (!!value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                  },
                }),
              ]}>
              <DatePicker
                placeholder={t("SELECT_END_DATE")}
                size="large"
                style={{ width: '100%', padding: '9px 11px' }}
                format="DD/MM/YYYY"
                minDate={minDate}
                onChange={setMaxDate}
              />
            </AntForm.Item>
          </Col>
          <Col xs={12} >
            <AntForm.Item label={t("IS_ACTIVATE")} name="isActive" valuePropName="checked">
              <Switch />
            </AntForm.Item>
          </Col>
        </Row>
      </AntForm>
    </AntModal >
  );
};

export default PromotionDetailModal;
