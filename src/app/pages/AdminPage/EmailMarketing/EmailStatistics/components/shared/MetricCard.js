import React from "react";
import { Card, Statistic } from "antd";
import { 
  ArrowUpOutlined, 
  ArrowDownOutlined,
  MailOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  PercentageOutlined
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";

const MetricCard = ({
  title,
  value,
  icon,
  trend,
  color = "blue",
  loading = false,
  suffix,
  precision,
  prefix,
  description
}) => {
  const { t } = useTranslation();

  // Get icon component
  const getIconComponent = (iconName) => {
    const iconMap = {
      'MailOutlined': MailOutlined,
      'EyeOutlined': EyeOutlined,
      'CheckCircleOutlined': CheckCircleOutlined,
      'PercentageOutlined': PercentageOutlined
    };
    const IconComponent = iconMap[iconName] || MailOutlined;
    return <IconComponent />;
  };

  // Format number with thousands separator
  const formatNumber = (num) => {
    if (num === null || num === undefined) return '0';
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const iconComponent = typeof icon === 'string' ? getIconComponent(icon) : icon;

  return (
    <Card
      className={`metric-card metric-card--${color}`}
      loading={loading}
      hoverable
      bordered={false}
    >
      <div className="metric-card__container">
        <div className="metric-card__header">
          <div className="metric-card__icon">
            {iconComponent}
          </div>
          {trend && (
            <div className={`metric-card__trend ${trend.direction === 'up' ? 'positive' : 'negative'}`}>
              {trend.direction === 'up' ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              <span className="trend-value">{trend.value}%</span>
            </div>
          )}
        </div>

        <div className="metric-card__content">
          <div className="metric-card__title">
            {t(title)}
          </div>
          <div className="metric-card__value">
            {formatNumber(value)}{suffix}
          </div>
          {description && (
            <div className="metric-card__description">
              {t(description)}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default MetricCard;
