.email-statistics-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles following FeedbackAnalysis pattern
  .email-marketing-info-card,
  .email-marketing-search-card,
  .email-marketing-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .email-marketing-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .email-marketing-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .email-marketing-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }
  }

  // Filter section
  .email-marketing-search-card {
    .form-filter {
      width: 100%;

      .search-form-item {
        margin-bottom: 16px;
      }

      .filter-form__date-picker {
        width: 100%;
        padding: 9px 11px !important;
      }

      .search-buttons-row {
        margin-top: 8px;
      }

      .search-buttons {
        display: flex;
        gap: 16px;
        justify-content: flex-end;
      }

      // Responsive styles
      @media (max-width: 768px) {
        .search-buttons {
          margin-top: 16px;
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }

  // Metric Cards
  .metric-card {
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    position: relative;
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      transform: translateY(-4px);

      &:before {
        opacity: 1;
      }
    }

    .ant-card-body {
      padding: 20px;
      height: 100%;
    }

    &__container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 12px;
      background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.05));
      backdrop-filter: blur(10px);
      border: 1px solid rgba(24, 144, 255, 0.1);
      position: relative;
      flex-shrink: 0;

      &:before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 12px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
        pointer-events: none;
      }

      .anticon {
        font-size: 18px;
        color: #1890ff;
        z-index: 1;
        position: relative;
      }
    }

    &__trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      font-weight: 600;
      padding: 6px 12px;
      border-radius: 20px;
      backdrop-filter: blur(10px);

      &.positive {
        color: #52c41a;
        background: linear-gradient(135deg, rgba(82, 196, 26, 0.15), rgba(82, 196, 26, 0.05));
        border: 1px solid rgba(82, 196, 26, 0.2);
      }

      &.negative {
        color: #ff4d4f;
        background: linear-gradient(135deg, rgba(255, 77, 79, 0.15), rgba(255, 77, 79, 0.05));
        border: 1px solid rgba(255, 77, 79, 0.2);
      }

      .trend-value {
        margin-left: 2px;
      }
    }

    &__content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    &__title {
      font-size: 12px;
      font-weight: 600;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      line-height: 1.2;
    }

    &__value {
      font-size: 28px;
      font-weight: 700;
      line-height: 1.1;
      color: var(--typo-colours-primary-black);
      margin-bottom: 6px;
      background: linear-gradient(135deg, var(--typo-colours-primary-black), #666);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    &__description {
      font-size: 11px;
      color: var(--typo-colours-secondary-grey);
      line-height: 1.3;
      opacity: 0.7;
    }

    // Color variants
    &--blue {
      &:before {
        background: linear-gradient(90deg, #1890ff, #40a9ff);
      }

      .metric-card__icon {
        background: linear-gradient(135deg, rgba(24, 144, 255, 0.15), rgba(24, 144, 255, 0.05));
        border: 1px solid rgba(24, 144, 255, 0.2);

        &:before {
          border-radius: 12px;
        }

        .anticon {
          color: #1890ff;
        }
      }

      .metric-card__value {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    &--green {
      &:before {
        background: linear-gradient(90deg, #52c41a, #73d13d);
      }

      .metric-card__icon {
        background: linear-gradient(135deg, rgba(82, 196, 26, 0.15), rgba(82, 196, 26, 0.05));
        border: 1px solid rgba(82, 196, 26, 0.2);

        &:before {
          border-radius: 12px;
        }

        .anticon {
          color: #52c41a;
        }
      }

      .metric-card__value {
        background: linear-gradient(135deg, #52c41a, #73d13d);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    &--orange {
      &:before {
        background: linear-gradient(90deg, #faad14, #ffc53d);
      }

      .metric-card__icon {
        background: linear-gradient(135deg, rgba(250, 173, 20, 0.15), rgba(250, 173, 20, 0.05));
        border: 1px solid rgba(250, 173, 20, 0.2);

        &:before {
          border-radius: 12px;
        }

        .anticon {
          color: #faad14;
        }
      }

      .metric-card__value {
        background: linear-gradient(135deg, #faad14, #ffc53d);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    &--purple {
      &:before {
        background: linear-gradient(90deg, #722ed1, #9254de);
      }

      .metric-card__icon {
        background: linear-gradient(135deg, rgba(114, 46, 209, 0.15), rgba(114, 46, 209, 0.05));
        border: 1px solid rgba(114, 46, 209, 0.2);

        .anticon {
          color: #722ed1;
        }
      }

      .metric-card__value {
        background: linear-gradient(135deg, #722ed1, #9254de);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }

  // Overview metrics grid
  .overview-metrics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 32px;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  // Table styles
  .email-marketing-table-card {
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      margin-bottom: 16px;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: var(--background-light-background-2);
    }

    // Activity action styling
    .activity-action {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;

      .action-label {
        font-weight: 600;
      }

      &--opened {
        background-color: rgba(24, 144, 255, 0.1);
        color: #1890ff;
      }

      &--clicked {
        background-color: rgba(82, 196, 26, 0.1);
        color: #52c41a;
      }

      &--unsubscribed {
        background-color: rgba(255, 77, 79, 0.1);
        color: #ff4d4f;
      }
    }

    // Rate badge styling
    .rate-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;

      &.rate-good {
        background-color: rgba(82, 196, 26, 0.1);
        color: #52c41a;
      }

      &.rate-medium {
        background-color: rgba(250, 173, 20, 0.1);
        color: #faad14;
      }

      &.rate-low {
        background-color: rgba(255, 77, 79, 0.1);
        color: #ff4d4f;
      }
    }

    // Chart placeholder styling
    .chart-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px;
      background-color: var(--background-light-background-1);
      border-radius: 8px;
      border: 2px dashed #d9d9d9;

      .chart-info {
        text-align: center;
        color: var(--typo-colours-secondary-grey);

        p {
          margin: 8px 0;
          font-size: 14px;

          &:first-child {
            font-weight: 600;
            font-size: 16px;
            color: var(--typo-colours-primary-black);
          }
        }
      }
    }

    // Performance summary styling
    .performance-summary {
      .chart-note {
        margin-top: 24px;
        text-align: center;

        p {
          color: var(--typo-colours-secondary-grey);
          font-size: 14px;
          margin: 0;
        }
      }
    }

    // Device breakdown styling
    .device-breakdown {
      .device-item {
        padding: 16px;
        border-radius: 8px;
        background-color: var(--background-light-background-1);
        transition: all 0.3s ease;

        &:hover {
          background-color: var(--background-light-background-2);
          transform: translateY(-2px);
          box-shadow: var(--shadow-level-1);
        }

        .device-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .device-name {
            font-weight: 600;
            color: var(--typo-colours-primary-black);
            font-size: 14px;
          }

          .device-percentage {
            font-weight: 600;
            color: var(--typo-colours-secondary-grey);
            font-size: 14px;
          }
        }

        .device-count {
          margin-top: 8px;
          font-size: 12px;
          color: var(--typo-colours-secondary-grey);
          text-align: center;
        }
      }
    }
  }

  // Tabs styling
  .ant-tabs {
    .ant-tabs-tab {
      font-weight: 500;

      &.ant-tabs-tab-active {
        font-weight: 600;
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    gap: 16px;

    .email-marketing-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }
}
