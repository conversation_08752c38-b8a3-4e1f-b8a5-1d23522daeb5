import { useState, useCallback } from "react";
import { getEmailStatistics } from "@services/EmailMarketing";
import dayjs from "dayjs";

// Mock data fallback
const mockStatisticsData = {
  overview: {
    totalSent: 0,
    totalOpened: 0,
    totalClicked: 0,
    totalUnsubscribed: 0,
    openRate: 0,
    clickRate: 0,
    unsubscribeRate: 0
  },
  campaignPerformance: [

  ],
  timeSeriesData: [
    { date: '2025-05-01', sent: 0, opened: 0, clicked: 0 },
    { date: '2025-05-02', sent: 0, opened: 0, clicked: 0 },
    { date: '2025-05-03', sent: 0, opened: 0, clicked: 0 },
    { date: '2025-05-04', sent: 0, opened: 0, clicked: 0 },
    { date: '2025-05-05', sent: 0, opened: 0, clicked: 0 },
    { date: '2025-05-06', sent: 0, opened: 0, clicked: 0 },
    { date: '2025-05-07', sent: 0, opened: 0, clicked: 0 },
    { date: '2025-05-15', sent: 0, opened: 0, clicked: 0 },
  ],
  recentActivity: [
  ]
};

// API call function
const getStatisticsData = async (filters) => {
  console.log("Fetching statistics with filters:", filters);
  try {
    const response = await getEmailStatistics(filters);
    return response || mockStatisticsData;
  } catch (error) {
    console.error("Error fetching statistics:", error);
    return mockStatisticsData; // Fallback to mock data
  }
};

export const useStatistics = () => {
  const [statisticsData, setStatisticsData] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchStatistics = useCallback(async (filters = {}) => {
    setLoading(true);
    setError(null);

    try {
      // Prepare filters for API
      const { fromDate, toDate } = filters;
      console.log("fromDate", fromDate, "toDate", toDate);
      const preparedFilters = {
        ...filters,
        ...fromDate ? fromDate : {},
        ...toDate ? toDate : {},
      };

      const dataResponse = await getStatisticsData(preparedFilters);
      if (dataResponse) {
        setStatisticsData(dataResponse);
      }
    } catch (err) {
      setError(err);
      console.error("Error in fetchStatistics:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback((filters) => {
    return fetchStatistics(filters);
  }, [fetchStatistics]);

  return {
    statisticsData,
    isLoading,
    error,
    fetchStatistics,
    refetch
  };
};
