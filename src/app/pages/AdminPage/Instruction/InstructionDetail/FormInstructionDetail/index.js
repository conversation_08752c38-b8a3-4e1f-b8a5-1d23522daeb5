import React, { useEffect, useMemo, useState } from "react";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import { Col, Divider, Form, Input, InputNumber, Row, Select, Slider } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import clsx from "clsx";

import { BUTTON, LANG_OPTIONS, OUTPUT_TYPE, RESPONSE_FORMAT } from "@constant";
import { LINK } from "@link";

import { parseJsonToText, parseTextToJson } from "@common/dataConverter";

import AntButton from "@component/AntButton";
import { toast } from "@component/ToastProvider";

import { cloneObj, coverLangArrayToObject, coverLanguageObjectToArray } from "@common/functionCommons";

import { createInstruction, getInstructions, updateInstruction } from "@services/Instruction";
import { getAllKnowledge } from "@services/Knowledge";

import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";

import "./FormInstructionDetail.scss";

FormInstructionDetail.propTypes = {};



function FormInstructionDetail({ modelOptions, knowledgeData, outputTypeData, optionsData, instructionData }) {
  const { t } = useTranslation();
  const { id } = useParams();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [nameLanguage, setNameLanguage] = useState([]);
  const [subInstructionList, setSubInstructionList] = useState([]);
  const [isSubInstruction, setIsSubInstruction] = useState(false);

  // Handle sub instruction change
  const handleSubInstructionChange = (value) => {
    setIsSubInstruction(value);
  };

  useEffect(() => {
    getSubInstructionData();
  }, [id]);

  useEffect(() => {
    if (instructionData) {
      if (instructionData?.isSubInstruction) {
        setIsSubInstruction(instructionData?.isSubInstruction);
      }

      const { schemaInstruction } = instructionData;
      const knowledges = instructionData.knowledges.map((knowledge) => knowledge?._id);
      const optionIds = instructionData.optionIds.map((option) => option?._id);
      const outputTypeId = instructionData.outputTypeId?._id;
      const toText = parseJsonToText(schemaInstruction);
      let localization = instructionData?.localization ? coverLanguageObjectToArray(instructionData?.localization) : [{ lang: null }];
      delete localization?.lang;
      form.setFieldsValue({
        ...instructionData,
        schemaInstruction: toText,
        knowledges,
        optionIds,
        outputTypeId,
        localization,
        subInstructionIds: instructionData?.subInstructionIds?.map((subInstruction) => subInstruction?._id),
      });
    }
  }, [instructionData]);

  const getSubInstructionData = async () => {
    const query = { isSubInstruction: true };
    const dataResponse = await getInstructions(query);
    if (dataResponse) {
      setSubInstructionList(dataResponse.filter(item => item?._id !== id));
    }
  };



  const onSelect = (data, index, setData) => {
    const { value } = data;
    setData((pre) => {
      const newData = cloneObj(pre);
      newData[index] = value;
      return newData;
    });
  };


  const onFinish = async (values) => {
    const { schemaInstruction } = values;
    const toJson = parseTextToJson(schemaInstruction);
    const dataRequest = { ...values, schemaInstruction: toJson };
    let localization = coverLangArrayToObject(values?.localization);
    let dataResponse;
    delete localization?.lang;
    if (id) {
      dataResponse = await updateInstruction({ ...dataRequest, _id: id, localization }, true);
    } else {
      dataResponse = await createInstruction({ ...dataRequest, localization }, true);
      navigate(LINK.INSTRUCTION_DETAIL.format(dataResponse._id));
    }

    if (dataResponse) {
      const successMessage = id ? "UPDATE_FINE_TUNING_SUCCESS" : "CREATE_INSTRUCTION_SUCCESS";
      toast.success(successMessage);
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  const getAvailabeOptions = (index, options) => {
    const optionsSelected = options.filter((lang, optionIndex) => optionIndex !== index);
    return LANG_OPTIONS.filter((option) => !optionsSelected.some((selected) => selected.value === option.value));
  };

  const onRemove = (index, name, remove, setData) => {
    remove(index, name);
    setData((pre) => {
      const newData = cloneObj(pre);
      newData.splice(index, 1);
      return newData;
    });
  };

  return (
    <div className={"form-instruction-detail-container"}>
      <div className="form-instruction-detail-header">
        <span>{!id && "Create Instruction"}</span>
      </div>
      <Form onFinish={onFinish} layout="vertical" form={form} size={"large"}>
        <Row gutter={24}>
          <Col xs={24} md={24}>
            <Form.Item
              label="Name"
              name="shortName"
              rules={[{ required: true, message: "Name can't be blank!" }]}>
              <Input placeholder={"Enter name"}/>
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="System message"
              name="systemMessage"
            >
              <Input.TextArea autoSize={{ minRows: 1, maxRows: 20 }} placeholder={"Enter instruction"}/>
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              label="Instruction message"
              name="instruction"
              rules={[{ required: true, message: "Instruction can't be blank!" }]}
            >
              <Input.TextArea autoSize={{ minRows: 1, maxRows: 20 }} placeholder={"Enter instruction"}/>
            </Form.Item>
          </Col>

          <Col xs={24} md={6}>
            <Form.Item label="Output type" name="outputTypeId">
              <Select
                options={outputTypeData}
                placeholder={"Select output type data"}
                allowClear
                fieldNames={{ label: "name", value: "_id" }}
                optionFilterProp="name"
              />
            </Form.Item>
          </Col>
          <Col xs={24} md={6}>
            <Form.Item label="Temperature" name="temperature">
              <InputNumber
                min={0}
                max={1}
                step={0.1}
                placeholder={"Temperature"}
              />
            </Form.Item>
          </Col>
          <Col xs={24} md={6}>
            <Form.Item label="Chat type" name="chatType">
              <Select
                options={[
                  { value: "text", label: "text" },
                  { value: "image", label: "image" },
                ]}
                allowClear
                placeholder={"Select additional request"}
              />
            </Form.Item>
          </Col>
          <Col xs={24} md={6}>
            <Form.Item label="Show additional request" name="showAdditionalRequest">
              <Select
                options={[
                  { value: true, label: "Yes" },
                  { value: false, label: "No" },
                ]}
                allowClear
                placeholder={"Select additional request"}
              />
            </Form.Item>
          </Col>
          <Col xs={24} md={6}>
            <Form.Item label="Gpt model" name="gptModel">
              <Select options={modelOptions} allowClear placeholder={"Select gpt model"} showSearch/>
            </Form.Item>
          </Col>
          <Col xs={24} md={18}>
            <Form.Item label="Input message" name="inputMessage">
              <Input placeholder={"Enter input message"}/>
            </Form.Item>
          </Col>
          <Row gutter={24} style={{ margin: 0, width: "100%" }}>
            <Col xs={24} md={6}>
              <Form.Item label="Is sub instruction" name="isSubInstruction">
                <Select
                  options={[
                    { value: true, label: "Yes" },
                    { value: false, label: "No" },
                  ]}
                  allowClear
                  placeholder={"Select is sub instruction"}
                  onChange={handleSubInstructionChange}
                />
              </Form.Item>
            </Col>
            {!isSubInstruction && <Col xs={24} md={18}>
              <Form.Item label="Sub instructions" name="subInstructionIds">
                <Select
                  options={subInstructionList}
                  mode="multiple"
                  allowClear
                  placeholder={"Select sub instructions"}
                  fieldNames={{ label: "shortName", value: "_id" }}
                />
              </Form.Item>
            </Col>}
          </Row>
          <Col xs={24} md={12}>
            <Form.Item label="Options" name="optionIds">
              <Select
                options={optionsData}
                mode="multiple"
                placeholder={"Select option data"}
                allowClear
                fieldNames={{ label: "name", value: "_id" }}
                optionFilterProp="name"
              />
            </Form.Item>
          </Col>

          {!!id && <Col xs={24} lg={12}>
            <Form.Item label="Knowledge" name="knowledges">
              <Select
                options={knowledgeData}
                mode="multiple"
                placeholder={"Select knowledge data"}
                allowClear
                fieldNames={{ label: "name", value: "_id" }}
                optionFilterProp="name"
              />
            </Form.Item>
          </Col>}
          <Divider/>
          <Col span={24}>
            <Form.Item required className="localization">
              <Form.List name="localization" initialValue={[{ "lang": null }]}>
                {(fields, { add, remove }) => {
                  return (
                    <div className={"instruction-localization-container"}>
                      {fields.map(({ key, name, ...restField }, index) => (
                        <div key={index}>
                          <div className={"instruction-localization-item"}>
                            <Row gutter={24} key={key} className="items-baseline">
                              <Col xs={24} lg={6}>
                                <Form.Item
                                  {...restField}
                                  label={index == 0 ? "Language" : ""}
                                  name={[name, "lang"]}
                                >
                                  <Select
                                    options={getAvailabeOptions(index, nameLanguage)}
                                    placeholder="Select language"
                                    onSelect={(_, option) => onSelect(option, index, setNameLanguage)}
                                  />
                                </Form.Item>
                              </Col>
                              <Col xs={24} lg={18}>
                                <Form.Item
                                  {...restField}
                                  name={[name, "name"]}
                                  label={index == 0 ? "Name" : ""}
                                >
                                  <Input placeholder="Enter name"/>
                                </Form.Item>
                              </Col>
                            </Row>
                            <div className={"instruction-item-action"}>
                              <AntButton
                                type={BUTTON.WHITE}
                                shape={"circle"}
                                className={clsx("btn-cancel-add-tool-detail", { "first-actions": !index })}
                                icon={<CancelIcon/>}
                                size={"small"}
                                onClick={() => onRemove(index, name, remove, setNameLanguage)}
                              />
                            </div>
                          </div>
                        </div>
                      ))}

                      {fields.length < 2 && (
                        <div className={"add-instruction-actions"}>
                          <AntButton
                            shape={"circle"}
                            size={"large"}
                            type={BUTTON.WHITE_BLUE}
                            onClick={() => add()}
                            icon={<PlusIcon/>}
                          ></AntButton>
                        </div>
                      )}
                    </div>
                  );
                }}
              </Form.List>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={32} justify="center" className="gap-4">
          <Form.Item className="save-button">
            <AntButton type={BUTTON.WHITE} onClick={handleCancel}>
              {t("BACK")}
            </AntButton>
          </Form.Item>
          <Form.Item className="save-button">
            <AntButton type={BUTTON.DEEP_NAVY} htmlType="submit">
              {id ? t("SAVE") : t("CREATE")}
            </AntButton>
          </Form.Item>
        </Row>
      </Form>
    </div>
  );
}

export default FormInstructionDetail;