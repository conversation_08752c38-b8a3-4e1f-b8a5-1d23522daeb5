import React, { useEffect, useState } from "react";
import { Col, Form, Row, Select, Input, DatePicker, Switch, Card, Tooltip } from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { InfoCircleOutlined, SearchOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import queryString from "query-string";

import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON, getIsDeveloperOptions } from "@constant";

import { handleSearchParams } from "@src/common/functionCommons";

import "./Filter.scss";

const Filter = () => {
  const [formFilter] = Form.useForm();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [isShowSelectDate, setShowSelectDate] = useState(false);
  const [isShowCreatedDateRange, setShowCreatedDateRange] = useState(false);
  const [minDate, setMinDate] = useState(null);
  const [maxDate, setMaxDate] = useState(null);
  const [minCreatedDate, setMinCreatedDate] = useState(null);
  const [maxCreatedDate, setMaxCreatedDate] = useState(null);
  const [isSearchable, setSearchable] = useState(false);
  const [activeFilterType, setActiveFilterType] = useState(null); // 'activity' hoặc 'created'

  useEffect(() => {
    const { query } = handleSearchParams(location.search);
    handleQueryFromUrl(query);
  }, [location.search]);

  const handleQueryFromUrl = (query) => {
    const { time, fromDate, toDate, onlyActiveUser, createdAtStart, createdAtEnd, createdDateType } = query;

    const newQuery = {
      ...query,
      ...fromDate ? { fromDate: dayjs(fromDate * 1000) } : {},
      ...toDate ? { toDate: dayjs(toDate * 1000) } : {},
    };

    // Xử lý onlyActiveUser
    if (onlyActiveUser !== undefined) {
      newQuery.onlyActiveUser = onlyActiveUser === "true" ? true : (onlyActiveUser === "false" ? false : null);
    }

    // Xác định loại bộ lọc đang hoạt động
    if (time || fromDate || toDate) {
      setActiveFilterType('activity');
    } else if (createdDateType || createdAtStart || createdAtEnd) {
      setActiveFilterType('created');
    } else {
      setActiveFilterType(null);
    }

    // Xử lý ngày tạo
    if (createdAtStart && createdAtEnd) {
      const startDate = dayjs(createdAtStart * 1000);
      const endDate = dayjs(createdAtEnd * 1000);
      setMinCreatedDate(startDate);
      setMaxCreatedDate(endDate);
      newQuery.createdAtStart = startDate;
      newQuery.createdAtEnd = endDate;
    }

    setShowSelectDate(time === "custom");
    setShowCreatedDateRange(createdDateType === "custom");
    setMinDate(newQuery?.fromDate);
    setMaxDate(newQuery?.toDate);
    formFilter.setFieldsValue(newQuery);
  };

  const onFilterSubmit = (values) => {
    const { fromDate, toDate, createdAtStart, createdAtEnd } = values;
    const repareValues = {
      ...values,
      ...fromDate ? { fromDate: dayjs(fromDate)?.startOf("day")?.unix() } : {},
      ...toDate ? { toDate: dayjs(toDate)?.endOf("day")?.unix() } : {},
    };

    // Xử lý ngày tạo
    if (createdAtStart && createdAtEnd) {
      repareValues.createdAtStart = dayjs(createdAtStart)?.startOf("day")?.unix();
      repareValues.createdAtEnd = dayjs(createdAtEnd)?.endOf("day")?.unix();
    }

    updateUrlQuery(repareValues);
  };

  const updateUrlQuery = (dataSearch = {}) => {
    let searchParams = new URLSearchParams();
    Object.entries(dataSearch).forEach(([key, value]) => {
      if (value !== null && value !== undefined) searchParams.append(key, value);
    });
    navigate(`?${searchParams.toString()}`, { replace: true });
    setSearchable(false);
  };

  const clearFormFilter = () => {
    setShowSelectDate(false);
    setShowCreatedDateRange(false);
    setActiveFilterType(null);
    formFilter.setFieldsValue({
      fullName: null,
      email: null,
      time: null,
      createdDateType: null,
      fromDate: null,
      toDate: null,
      createdAtStart: null,
      createdAtEnd: null,
      onlyActiveUser: null,
      isDeveloper: null
    });
    setSearchable(true);
  };

  const onFormChange = () => {
    setSearchable(true);
  };

  const hanldeChangeSelectTime = (value) => {
    if (value) {
      // Nếu chọn ACTIVITY_PERIOD, xóa các trường CREATED_DATE
      formFilter.setFieldsValue({
        createdDateType: null,
        createdAtStart: null,
        createdAtEnd: null
      });
      setActiveFilterType('activity');

      if (value === "custom") {
        setShowSelectDate(true);
      } else {
        setShowSelectDate(false);
        formFilter.setFieldsValue({ fromDate: null, toDate: null });
      }
    } else {
      setShowSelectDate(false);
      setActiveFilterType(null);
    }
  };

  const handleChangeCreatedDateSelect = (value) => {
    if (value) {
      // Nếu chọn CREATED_DATE, xóa các trường ACTIVITY_PERIOD
      formFilter.setFieldsValue({
        time: null,
        fromDate: null,
        toDate: null
      });
      setActiveFilterType('created');

      if (value === "custom") {
        setShowCreatedDateRange(true);
      } else {
        setShowCreatedDateRange(false);
        formFilter.setFieldsValue({ createdAtStart: null, createdAtEnd: null });
      }
    } else {
      setShowCreatedDateRange(false);
      setActiveFilterType(null);
    }
  };

  return (
    <Card className="user-tracking-filter-card">
      <AntForm
        form={formFilter}
        size={"large"}
        className={"filter-form-user-tracking"}
        onFinish={onFilterSubmit}
        onValuesChange={onFormChange}
      >
        <Row gutter={24} className="grow">
          <Col xs={24} md={12} lg={6}>
            <AntForm.Item name={"fullName"} className="search-form-item">
              <Input
                placeholder={t("ENTER_FULL_NAME")}
                allowClear
                prefix={<SearchOutlined />}
              />
            </AntForm.Item>
          </Col>
          <Col xs={24} md={12} lg={6}>
            <AntForm.Item name={"email"} className="search-form-item">
              <Input
                placeholder={t("ENTER_EMAIL")}
                allowClear
                prefix={<SearchOutlined />}
              />
            </AntForm.Item>
          </Col>
          <Col xs={24} md={12} lg={6}>
            <AntForm.Item name={"isDeveloper"} className="search-form-item">
              <Select
                placeholder={t("SELECT_USER_TYPE")}
                options={getIsDeveloperOptions()}
                allowClear
              />
            </AntForm.Item>
          </Col>
          <Col xs={24} md={12} lg={6}>
            <AntForm.Item name={"onlyActiveUser"} className="search-form-item" label={t("USER_STATUS")} valuePropName="checked">
              <Switch
                checkedChildren={t("ACTIVE")}
                unCheckedChildren={t("ALL")}
                className="user-status-switch"
              />
            </AntForm.Item>
          </Col>
          <Col xs={24} md={12} lg={6}>
            <AntForm.Item
              name={"createdDateType"}
              className="search-form-item"
            >
              <Select
                placeholder={
                  <span>
                    {t("CREATED_DATE")}
                    {activeFilterType === 'activity' && (
                      <Tooltip title={t("CANNOT_USE_BOTH_FILTERS")}>
                        <InfoCircleOutlined className="filter-tooltip-icon disabled" />
                      </Tooltip>
                    )}
                  </span>
                }
                onChange={handleChangeCreatedDateSelect}
                allowClear
                disabled={activeFilterType === 'activity'}
              >
                <Select.Option value={"week"}>{t("THIS_WEEK")}</Select.Option>
                <Select.Option value={"month"}>{t("THIS_MONTH")}</Select.Option>
                <Select.Option value={"custom"}>{t("CUSTOM")}</Select.Option>
              </Select>
            </AntForm.Item>
          </Col>
          {isShowCreatedDateRange && <>
            <Col xs={24} md={12} lg={6}>
              <AntForm.Item
                name={"createdAtStart"}
                className="search-form-item"
                rules={[
                  () => ({
                    validator(_, value) {
                      if (!!value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                    },
                  }),
                ]}
              >
                <DatePicker
                  placeholder={t("SELECT_FROM_DATE")}
                  className="filter-form__date-picker"
                  format="DD/MM/YYYY"
                  maxDate={maxCreatedDate}
                  onChange={setMinCreatedDate}
                />
              </AntForm.Item>
            </Col>
            <Col xs={24} md={12} lg={6}>
              <AntForm.Item
                name={"createdAtEnd"}
                className="search-form-item"
                rules={[
                  () => ({
                    validator(_, value) {
                      if (!!value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                    },
                  }),
                ]}>
                <DatePicker
                  placeholder={t("SELECT_TO_DATE")}
                  className="filter-form__date-picker"
                  format="DD/MM/YYYY"
                  minDate={minCreatedDate}
                  onChange={setMaxCreatedDate}
                />
              </AntForm.Item>
            </Col>
          </>}
          <Col xs={24} md={12} lg={6}>
            <AntForm.Item
              name={"time"}
              className="search-form-item"
            >
              <Select
                placeholder={
                  <span>
                    {t("ACTIVITY_PERIOD")}
                    {activeFilterType === 'created' && (
                      <Tooltip title={t("CANNOT_USE_BOTH_FILTERS")}>
                        <InfoCircleOutlined className="filter-tooltip-icon disabled" />
                      </Tooltip>
                    )}
                  </span>
                }
                onChange={hanldeChangeSelectTime}
                allowClear
                disabled={activeFilterType === 'created'}
              >
                <Select.Option value={"week"}>{t("THIS_WEEK")}</Select.Option>
                <Select.Option value={"month"}>{t("THIS_MONTH")}</Select.Option>
                <Select.Option value={"custom"}>{t("CUSTOM")}</Select.Option>
              </Select>
            </AntForm.Item>
          </Col>

          {isShowSelectDate && <>
            <Col xs={24} md={12} lg={6}>
              <AntForm.Item
                name={"fromDate"}
                className="search-form-item"
                rules={[
                  () => ({
                    validator(_, value) {
                      if (!!value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                    },
                  }),
                ]}
              >
                <DatePicker
                  placeholder={t("SELECT_FROM_DATE")}
                  className="filter-form__date-picker"
                  format="DD/MM/YYYY"
                  maxDate={maxDate}
                  onChange={setMinDate}
                />
              </AntForm.Item>
            </Col>
            <Col xs={24} md={12} lg={6}>
              <AntForm.Item
                name={"toDate"}
                className="search-form-item"
                rules={[
                  () => ({
                    validator(_, value) {
                      if (!!value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                    },
                  }),
                ]}>
                <DatePicker
                  placeholder={t("SELECT_TO_DATE")}
                  className="filter-form__date-picker"
                  format="DD/MM/YYYY"
                  minDate={minDate}
                  onChange={setMaxDate}
                />
              </AntForm.Item>
            </Col>
          </>}
        </Row>
        <Row justify="end" className="search-buttons-row">
          <Col>
            <div className={"search-buttons"}>
              <AntButton type={BUTTON.GHOST_WHITE} onClick={clearFormFilter}>{t("CLEAR")}</AntButton>
              <AntButton type={BUTTON.DEEP_NAVY} htmlType={"submit"} disabled={!isSearchable}>{t("SEARCH")}</AntButton>
            </div>
          </Col>
        </Row>
      </AntForm>
    </Card>
  );
};

export default Filter;
