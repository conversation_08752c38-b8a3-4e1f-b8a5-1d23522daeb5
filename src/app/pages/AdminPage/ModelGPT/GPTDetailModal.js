import React, { useEffect, useState } from "react";
import { Col, Form, Input, InputNumber, Row, Select } from "antd";
import { useTranslation } from "react-i18next";
import clsx from "clsx";

import AntButton from "@component/AntButton";
import CancelIcon from "@component/SvgIcons/CancelIcon";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import CustomModal from "@component/CustomModal";

import { cloneObj, coverLangArrayToObject, coverLanguageObjectToArray } from "@common/functionCommons";

import { BUTTON, INPUT_TYPE, LANG_OPTIONS, RULES } from "@constant";

import "./ModalDetail.scss";


const GPTDetailModal = ({ ...props }) => {
  const { t } = useTranslation();
  const { isShowModal, gptModel, apiKeyData } = props;
  const [formOption] = Form.useForm();
  
  useEffect(() => {
    if (gptModel) {
      formOption.setFieldsValue(gptModel);
    }
  }, [gptModel]);
  
  const onFinish = async (values) => {
    let data = { ...values };
    if (gptModel) {
      data = { ...data, _id: gptModel._id };
    }
    await props.handleOk(data);
    formOption.resetFields();
  };
  
  const handleCancel = () => {
    formOption.resetFields();
    props.handleCancel();
  };
  
  return (
    <CustomModal
      isShowModal={isShowModal}
      closeIcon
      handleCancel={handleCancel}
      className="option-modal"
      form="option-form"
      footerAlign="center"
      width={800}
      okText={gptModel ? t("UPDATE") : t("CREATE")}
      title={gptModel ? "Option detail" : "Create Option"}
    
    >
      <div className="option-modal__content">
        <div className="option-modal__body">
          <Form id="option-form" onFinish={onFinish} layout="vertical" size={"large"} form={formOption}>
            <Row gutter={24}>
              <Col xs={24} lg={24}>
                <Form.Item
                  label="Model name"
                  name="gptModel"
                  rules={[{ required: true, message: "Model name can't be blank!" }]}
                >
                  <Input placeholder={"Enter gpt model"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={24}>
                <Form.Item
                  label="API key"
                  name="apiKeyIds"
                  rules={[{ required: true, message: "API key can't be blank!" }]}
                >
                  <Select
                    options={apiKeyData}
                    mode="multiple"
                    placeholder={"Select api key"}
                    allowClear
                    fieldNames={{ label: "apiKey", value: "_id" }}
                    optionFilterProp="apiKey"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item
                  label="Token unit"
                  name="tokenUnit"
                  rules={[{ required: true, message: "Token unit can't be blank!" }]}
                >
                  <InputNumber placeholder={"Enter token unit"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item
                  label="Unit"
                  name="unit"
                  rules={[{ required: true, message: "Unit can't be blank!" }]}
                >
                  <Input placeholder={"Enter unit"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item
                  label="Input price"
                  name="priceInput"
                  rules={[{ required: true, message: "Input price can't be blank!" }]}
                >
                  <Input placeholder={"Enter input price"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item
                  label="Output price"
                  name="priceOutput"
                  rules={[{ required: true, message: "Output price can't be blank!" }]}
                >
                  <Input placeholder={"Enter output price"}/>
                </Form.Item>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item
                  label="Max tokens"
                  name="maxTokens"
                  rules={[{ required: true, message: "Max tokens can't be blank!" }]}
                >
                  <InputNumber
                    placeholder={"Enter max tokens"}
                    controls={false}
                    changeOnBlur
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    </CustomModal>
  );
};

export default GPTDetailModal;
