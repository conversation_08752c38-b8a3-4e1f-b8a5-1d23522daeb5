import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router-dom";
import { Card, Form, Input, Row, Col, Select, Tag, Tooltip } from "antd";
import { SearchOutlined, EditOutlined, PlusOutlined } from "@ant-design/icons";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import AntButton from "@component/AntButton";
import { AntForm } from "@component/AntForm";
import TableAdmin from "@src/app/component/TableAdmin";
import Loading from "@component/Loading";
import { toast } from "@component/ToastProvider";
import { confirm } from "@component/ConfirmProvider";

import { LINK } from "@link";
import { BUTTON, PAGINATION_INIT, DIFFICULTY_OPTIONS, EXERCISE_TYPE_OPTIONS, EXERCISE_STATUS_OPTIONS } from "@constant";

import { handlePagingData } from "@common/dataConverter";
import { handleSearchParams, navigateAfterDelete, orderColumn, paginationConfig, handleReplaceUrlSearch } from "@common/functionCommons";

import { createExercise, deleteExercise, getPaginationExercise } from "@services/DictationAndShadowing";

import "./DictationAndShadowing.scss";


const DictationAndShadowing = ({ ...props }) => {
  const { t, i18n } = useTranslation();
  const location = useLocation();

  const [exerciseData, setExerciseData] = useState(PAGINATION_INIT);
  const [isShowCreate, setShowCreate] = useState(false);
  const [formFilter] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);


  useEffect(() => {
    const { paging, query } = handleSearchParams(location.search);
    formFilter.setFieldsValue(query);
    getExerciseData(paging, query);
  }, [location.search]);


  async function getExerciseData(
    paging = exerciseData.paging,
    query = exerciseData.query,
  ) {
    setIsLoading(true);
    const apiResponse = await getPaginationExercise(paging, query);
    if (apiResponse) {
      setExerciseData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  }

  async function handleDeleteExercise(exerciseId, exerciseName) {
    confirm.delete({
      title: t("DELETE_EXERCISE"),
      content: t("DELETE_EXERCISE_CONFIRM", { title: exerciseName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setIsLoading(true);
        const apiResponse = await deleteExercise(exerciseId);
        if (apiResponse) {
          toast.success(t("DELETE_EXERCISE_SUCCESS"));
          navigateAfterDelete(exerciseData);
        } else {
          toast.error(t("DELETE_EXERCISE_ERROR"));
          setIsLoading(false);
        }
      },
    });
  }

  async function handleCreateExercise(values, file) {
    setIsLoading(true);
    const apiResponse = await createExercise(values);
    if (apiResponse) {
      toast.success(t("CREATE_EXERCISE_SUCCESS"));
      await getExerciseData();
      setShowCreate(false);
    } else {
      toast.error(t("CREATE_EXERCISE_ERROR"));
      setIsLoading(false);
    }
  }

  const onSubmitFilter = (values) => {
    handleReplaceUrlSearch(1, exerciseData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    formFilter.resetFields();
    handleReplaceUrlSearch(1, exerciseData.paging.pageSize, {});
  };

  return (
    <Loading active={isLoading} transparent>
      <div className="dictation-shadowing-container">
        <Card className="dictation-shadowing-info-card">
          <div className="dictation-shadowing-info-header">
            <div>
              <h1 className="dictation-shadowing-title">{t("DICTATION_SHADOWING_MANAGEMENT")}</h1>
              <p className="dictation-shadowing-description">{t("DICTATION_SHADOWING_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <Link to={LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT_CREATE}>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="large"
                className="btn-create-exercise"
                icon={<PlusOutlined/>}
              >
                {t("CREATE_EXERCISE")}
              </AntButton>
            </Link>
          </div>
        </Card>

        <Card className="dictation-shadowing-search-card">
          <AntForm form={formFilter} layout="horizontal" size={"large"} className="form-filter" onFinish={onSubmitFilter}>
            <Row gutter={16} align="middle" justify="space-between">
              <Col xs={24} md={16} lg={16}>
                <Row gutter={16}>
                  <Col xs={24} md={6} lg={6}>
                    <AntForm.Item name="name" className="search-form-item">
                      <Input
                        placeholder={t("SEARCH_EXERCISE_NAME_PLACEHOLDER")}
                        allowClear
                        prefix={<SearchOutlined />}
                        autoComplete="off"
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={6} lg={6}>
                    <AntForm.Item name="difficulty" className="search-form-item">
                      <Select
                        options={DIFFICULTY_OPTIONS}
                        allowClear
                        placeholder={t("FILTER_BY_DIFFICULTY")}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={6} lg={6}>
                    <AntForm.Item name="type" className="search-form-item">
                      <Select
                        options={EXERCISE_TYPE_OPTIONS}
                        allowClear
                        placeholder={t("FILTER_BY_TYPE")}
                      />
                    </AntForm.Item>
                  </Col>
                  <Col xs={24} md={6} lg={6}>
                    <AntForm.Item name="status" className="search-form-item">
                      <Select
                        options={EXERCISE_STATUS_OPTIONS}
                        allowClear
                        placeholder={t("FILTER_BY_STATUS")}
                      />
                    </AntForm.Item>
                  </Col>
                </Row>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton type={BUTTON.GHOST_WHITE} size="large" onClick={onClearFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} size="large" htmlType="submit">
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="dictation-shadowing-table-card">
          <TableAdmin
            scroll={{ x: 1000 }}
            columns={[
              orderColumn(exerciseData.paging),
              {
                title: t("NAME"),
                dataIndex: "name",
                width: 250,
                render: (text) => <span className="exercise-title-value">{text}</span>,
              },
              { title: t("DIFFICULTY"), dataIndex: "difficulty", width: 80 },
              { title: t("TYPE"), dataIndex: "type", width: 100 },
              { title: t("TAG"), dataIndex: "tag", width: 100 },
              { title: t("TIME_LIMIT"), dataIndex: "timeLimit", width: 80 },
              {
                title: t("STATUS"),
                dataIndex: "status",
                width: 150,
                render: (status) => {
                  let color = "default";
                  let className = "status-tag-default";

                  if (status === "published") {
                    color = "success";
                    className = "status-tag-published";
                  } else if (status === "draft") {
                    color = "warning";
                    className = "status-tag-draft";
                  } else if (status === "hidden") {
                    color = "error";
                    className = "status-tag-hidden";
                  }

                  return (
                    <Tag color={color} className={className}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Tag>
                  );
                },
              },
              {
                title: t("CREATED_BY"),
                dataIndex: ["createdBy", "fullName"],
                width: 150,
              },
              {
                title: t("ACTION"),
                width: 120,
                align: "center",
                render: (_, record) => (
                  <div className="exercise-actions">
                    <Tooltip title={t("EDIT_EXERCISE")}>
                      <Link to={LINK.ADMIN.DICTATION_SHADOWING_MANAGEMENT_ID.format(record._id)}>
                        <AntButton
                          type={BUTTON.GHOST_WHITE}
                          size="small"
                          className="btn-edit-exercise"
                          icon={<EditOutlined/>}
                        />
                      </Link>
                    </Tooltip>
                    <Tooltip title={t("DELETE_EXERCISE")}>
                      <AntButton
                        type={BUTTON.GHOST_WHITE}
                        size="small"
                        className="btn-delete-exercise"
                        icon={<DeleteIcon/>}
                        onClick={() => handleDeleteExercise(record._id, record.name)}
                      />
                    </Tooltip>
                  </div>
                ),
              },
            ]}
            dataSource={exerciseData.rows}
            pagination={paginationConfig(exerciseData.paging, exerciseData.query, i18n.language)}
            className="dictation-shadowing-table"
            rowClassName={() => "dictation-shadowing-table-row"}
            locale={{ emptyText: t("NO_EXERCISES_FOUND") }}
          />
        </Card>
      </div>
    </Loading>
  );
};

export default DictationAndShadowing;
