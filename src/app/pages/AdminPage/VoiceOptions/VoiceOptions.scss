.voice-options-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .voice-options-info-card,
  .voice-options-search-card,
  .voice-options-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .voice-options-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .voice-options-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .voice-options-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-voice-option {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .form-filter {
    width: 100%;
  }

  // Table styles
  .voice-options-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .voice-options-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .voice-name-value {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      font-size: 14px;
    }

    .voice-options-actions {
      display: flex;
      flex-direction: row;
      gap: 8px;
      justify-content: center;

      .btn-edit-voice-option,
      .btn-delete-voice-option {
        &:hover {
          background: var(--background-hover);
        }
      }
    }

    .status-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;

      &.status-badge-success {
        background-color: var(--green-light-1);
        color: var(--green-dark);
      }

      &.status-badge-default {
        background-color: var(--background-light-background-grey);
        color: var(--typo-colours-support-grey-light);
      }
    }

    .no-audio {
      color: var(--typo-colours-support-grey-light);
      font-style: italic;
      font-size: 12px;
    }

    .preview-audio-container {
      display: flex;
      justify-content: center;

      .hidden-audio {
        display: none;
      }

      .audio-play-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 12px;
        border-radius: 4px;
        transition: all 0.3s ease;

        .audio-play-icon {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }

        .audio-play-text {
          font-size: 14px;
          font-weight: 500;
          color: white;
        }

        &:hover {
          opacity: 0.9;
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .voice-options-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create-voice-option {
        width: 100%;
        justify-content: center;
      }
    }

    .search-buttons {
      margin-top: 16px;
      width: 100%;
      justify-content: space-between;
    }
  }
}