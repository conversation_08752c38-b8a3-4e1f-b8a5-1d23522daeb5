import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Card, Col, Form, Input, Row, Tooltip } from "antd";
import { CheckOutlined, EditOutlined, PlusOutlined, SearchOutlined, StopOutlined } from "@ant-design/icons";
import { useNavigate } from 'react-router-dom';

import PLAY_WHITE from "@src/asset/icon/play/play-white.svg";
import PAUSE_WHITE from "@src/asset/icon/pause/pause-white.svg";

import { BUTTON } from "@constant";
import { API } from "@api";

import { getVoiceOptions, deleteVoiceOption } from "@services/VoiceOptions";

import { confirm } from "@component/ConfirmProvider";
import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";
import TableAdmin from "@src/app/component/TableAdmin";
import { AntForm } from "@component/AntForm";

import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import "./VoiceOptions.scss";
import { LINK } from "@link";
import { Link } from "react-router-dom";
import Loading from "@component/Loading";


const VoiceOptions = (props) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [voicesData, setVoicesData] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [formSearch] = Form.useForm();
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    getVoiceslData();
  }, []);

  async function getVoiceslData() {
    setLoading(true);
    const apiResponse = await getVoiceOptions();
    if (apiResponse) {
      setVoicesData(apiResponse);
    }
    setLoading(false);
  }

  const handleCreate = () => {
    navigate(LINK.ADMIN_VOICE_OPTION_CREATE);
  };

  const handleDelete = (voiceOptionId, voiceName) => {
    confirm.delete({
      title: t("DELETE_VOICE_OPTION"),
      content: t("DELETE_VOICE_OPTION_CONFIRM_WITH_NAME", { name: voiceName }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async (e) => {
        setLoading(true);
        try {
          const apiResponse = await deleteVoiceOption(voiceOptionId);
          if (apiResponse) {
            await getVoiceslData();
            toast.success(t("DELETE_VOICE_OPTION_SUCCESS"));
          }
        } catch (error) {
          toast.error(t("DELETE_VOICE_OPTION_ERROR"));
          setLoading(false);
        }
      },
    });
  };

  const submitFormFilter = (values) => {
    setSearchQuery(values.searchTerm || "");
  };

  const onClearFilter = () => {
    formSearch.resetFields();
    setSearchQuery("");
  };

  // Filter data based on search query
  const filteredData = searchQuery
    ? voicesData.filter(voice =>
        voice.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        voice.code.toLowerCase().includes(searchQuery.toLowerCase()))
    : voicesData;

  const columns = [
    {
      title: t("ORDER"),
      align: "center",
      width: 80,
      render: (value, row, index) => index + 1,
    },
    {
      title: t("NAME"),
      dataIndex: "name",
      width: 150,
      render: (text) => <span className="voice-name-value">{text}</span>,
    },
    {
      title: t("CODE"),
      dataIndex: "code",
      width: 120,
    },
    {
      title: t("VOICE_FILE"),
      dataIndex: ["voiceFileId", "displayName"],
      width: 250,
      ellipsis: true,
    },
    {
      title: t("IS_PUBLIC"),
      dataIndex: "isPublic",
      width: 100,
      align: "center",
      render: (value) => (
        <span className={`status-badge ${value ? 'status-badge-success' : 'status-badge-default'}`}>
          {value ? t("YES") : t("NO")}
        </span>
      ),
    },
    {
      title: t("AUDIO_PREVIEW"),
      width: 150,
      align: "center",
      render: (_, record) => {
        if (!record.voiceFileId) {
          return <span className="no-audio">{t("NO_AUDIO")}</span>;
        }

        // Sử dụng biến toàn cục để lưu trữ audio đang phát
        if (!window.audioPlayers) {
          window.audioPlayers = {};
        }

        const handlePlayPause = () => {
          const buttonId = `play-button-${record._id}`;
          const button = document.getElementById(buttonId);
          if (!button) return;

          const icon = button.querySelector('img');
          const text = button.querySelector('span');

          // Kiểm tra xem âm thanh có đang phát không
          if (window.audioPlayers[record._id] && !window.audioPlayers[record._id].paused) {
            // Đang phát -> tạm dừng
            window.audioPlayers[record._id].pause();
            if (icon) icon.src = PLAY_WHITE;
            if (text) text.innerText = t("PLAY_AUDIO");
          } else {
            // Tạm dừng hoặc chưa phát -> phát

            // Dừng tất cả các audio đang phát khác
            Object.keys(window.audioPlayers).forEach(key => {
              if (key !== record._id && window.audioPlayers[key] && !window.audioPlayers[key].paused) {
                window.audioPlayers[key].pause();

                // Đặt lại giao diện các nút khác
                const otherButton = document.getElementById(`play-button-${key}`);
                if (otherButton) {
                  const otherIcon = otherButton.querySelector('img');
                  const otherText = otherButton.querySelector('span');
                  if (otherIcon) otherIcon.src = PLAY_WHITE;
                  if (otherText) otherText.innerText = t("PLAY_AUDIO");
                }
              }
            });

            // Tạo mới hoặc sử dụng lại audio đã có
            if (!window.audioPlayers[record._id]) {
              window.audioPlayers[record._id] = new Audio(API.STREAM_MEDIA.format(record.voiceFileId._id));

              // Khi phát xong, đặt lại giao diện
              window.audioPlayers[record._id].onended = () => {
                if (icon) icon.src = PLAY_WHITE;
                if (text) text.innerText = t("PLAY_AUDIO");
              };
            }

            // Phát audio
            window.audioPlayers[record._id].play();

            // Cập nhật giao diện
            if (icon) icon.src = PAUSE_WHITE;
            if (text) text.innerText = t("PAUSE_AUDIO");
          }
        };

        return (
          <div className="preview-audio-container">
            <AntButton
              id={`play-button-${record._id}`}
              type={BUTTON.DEEP_NAVY}
              size="small"
              className="audio-play-button"
              onClick={handlePlayPause}
            >
              <img src={PLAY_WHITE} alt="play" className="audio-play-icon" />
              <span className="audio-play-text">{t("PLAY_AUDIO")}</span>
            </AntButton>
          </div>
        );
      },
    },
    {
      title: t("ACTION"),
      key: "action",
      width: 120,
      align: "center",
      render: (_, record) => (
        <div className="voice-options-actions">
          <Tooltip title={t("EDIT_VOICE_OPTION")}>
            <Link to={LINK.ADMIN_VOICE_OPTION_DETAIL.format(record._id)}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className={"btn-edit-voice-option"}
                icon={<EditOutlined />}
              />
            </Link>
          </Tooltip>
          <Tooltip title={t("DELETE_VOICE_OPTION")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-voice-option"}
              icon={<DeleteIcon />}
              onClick={() => handleDelete(record?._id, record?.name)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  return (
    <Loading active={isLoading} transparent>
      <div className="voice-options-container">
        <Card className="voice-options-info-card">
          <div className="voice-options-info-header">
            <div>
              <h1 className="voice-options-title">{t("VOICE_OPTIONS_MANAGEMENT")}</h1>
              <p className="voice-options-description">{t("VOICE_OPTIONS_DESCRIPTION")}</p>
            </div>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size={"large"}
              icon={<PlusOutlined />}
              onClick={handleCreate}
              className="btn-create-voice-option"
            >
              {t("CREATE_VOICE_OPTION")}
            </AntButton>
          </div>
        </Card>

        <Card className="voice-options-search-card">
          <AntForm onFinish={submitFormFilter} layout="horizontal" form={formSearch} className="form-filter" size={"large"}>
            <Row gutter={24}>
              <Col xs={24} md={12} lg={16}>
                <AntForm.Item name="searchTerm" className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_VOICE_OPTION_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={12} lg={8} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="voice-options-table-card">
          <TableAdmin
            columns={columns}
            dataSource={filteredData}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showTotal: (total) => t("TOTAL_ITEMS", { total }),
            }}
            className={"voice-options-table"}
            scroll={{ x: 1000 }}
            rowClassName={() => "voice-options-table-row"}
            locale={{ emptyText: t("NO_VOICE_OPTIONS_FOUND") }}
          />
        </Card>
      </div>
    </Loading>
  );
};

export default VoiceOptions;
