import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { connect } from "react-redux";
import { Checkbox, Col, Form, Input, Row, Select } from "antd";
import { useTranslation } from "react-i18next";
import { CheckOutlined } from "@ant-design/icons";

import Loading from "@src/app/component/Loading";
import TableAdmin from "@src/app/component/TableAdmin";
import AntButton from "@src/app/component/AntButton";

import { toast } from "@src/app/component/ToastProvider";
import { paginationConfig, handleSearchParams, formatDate, handleReplaceUrlSearch, cloneObj, convertObjectToQuery, getColumnSortOrder } from "@src/common/functionCommons";

import { BUTTON, getIsDeveloperOptions, PAGINATION_INIT, USER_TYPE } from "@constant";
import { handlePagingData } from "@src/common/dataConverter";

import { AntForm } from "@src/app/component/AntForm";
import { findAllUser, updateDeveloperForUser } from "@src/app/services/User";


function User() {
  const location = useLocation();
  const [form] = Form.useForm();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const [userData, setUserData] = useState(PAGINATION_INIT);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const { query, paging } = handleSearchParams(location.search);
    getUserData(query, paging);
    form.setFieldsValue(query);
  }, [location.search]);

  const getUserData = async (query, paging) => {
    setIsLoading(true);
    const searchField=["email", "fullName"];
    const apiResponse = await findAllUser(paging, query, searchField);
    if (apiResponse) {
      setUserData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  };

  const onChangeCheckbox = async (record) => {
    setIsLoading(true);
    const updateResponse = await updateDeveloperForUser(record._id);
    if (updateResponse) {
      if (userData?.query.isDeveloper) {
        await getUserData(userData.query);
      } else {
        const rows = userData.rows.map(item => item._id === record._id ? {
          ...item,
          isDeveloper: !record.isDeveloper,
        } : item);
        setUserData({ ...userData, rows });
      }
      toast.success(t("USER_INFORMATION_UPDATED"));
    }
    setIsLoading(false);
  };

  //handle change sort table
  const handleChangeTable = async (pagination, filters, sorter, extra) => {
    if (extra.action === "sort") {
      const newQuerParams = cloneObj(userData?.query);
      if (sorter.order) {
        newQuerParams.sort = `${sorter.order === "ascend" ? "" : "-"}${sorter?.columnKey}`;
      } else {
        delete newQuerParams.sort;
      }
      navigate(`?${convertObjectToQuery(newQuerParams)}`, { replace: true });
    }
  };

  const columns = [
    {
      title: t("Email"),
      dataIndex: "email",
      width: 250,
    },
    {
      title: t("FULL_NAME"),
      dataIndex: "fullName",
      width: 250,
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      width: 200,
      render: (value) => formatDate(value),
      sortOrder: getColumnSortOrder("createdAt", userData?.query),
      sorter: true,
    },
    {
      title: t("USER_TYPE"),
      dataIndex: "type",
      render: value => t(value?.toUpperCase()),
      width: 150,
    },
    {
      title: t("STATE"),
      dataIndex: "state",
      width: 150,
    },
    {
      title: t("IS_ACTIVATE"),
      dataIndex: "active",
      width: 150,
      align: "center",
      render: (value) => (value && <CheckOutlined />),
    },
    {
      title: t("HAS_PASSWORD"),
      dataIndex: "hasPassword",
      width: 150,
      align: "center",
      render: (value) => (value && <CheckOutlined />),
    },
    {
      title: t("IS_A_DEVELOPER"),
      key: "isDeveloper",
      dataIndex: "isDeveloper",
      align: "center",
      width: 100,
      render: (_, record) => <Checkbox onChange={() => onChangeCheckbox(record)} checked={record.isDeveloper} />,
    },
    {
      title: t("ORGANIZATION"),
      dataIndex: ["organizationId", "name"],
      align: "center",
      width: 150,
    },
    {
      title: t("ROLE"),
      dataIndex: "role",
      align: "center",
      width: 150,
    },
  ];

  const pagination = paginationConfig(userData.paging, userData.query, i18n.language);

  const submitFormFilter = (values) => {
    handleReplaceUrlSearch(1, userData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    form.resetFields();
    submitFormFilter({});
  };

  return (
    <div className="options-container">
      <div >
        <AntForm form={form} layout="vertical" size={"large"} onFinish={submitFormFilter}>
          <Row gutter={24} >
            <Col xs={24} lg={18}>
              <Row gutter={24}>
                <Col xs={6}>
                  <AntForm.Item name="email">
                    <Input placeholder={t("EMAIL")} allowClear />
                  </AntForm.Item>
                </Col>
                <Col xs={6}>
                  <AntForm.Item name="fullName">
                    <Input placeholder={t("FULL_NAME")} allowClear />
                  </AntForm.Item>
                </Col>
                <Col xs={6}>
                  <AntForm.Item name="type">
                    <Select placeholder={t("USER_TYPE")} allowClear>
                      <Select.Option value={USER_TYPE.STUDENT}>{t("STUDENT")}</Select.Option>
                      <Select.Option value={USER_TYPE.TEACHER}>{t("TEACHER")}</Select.Option>
                    </Select>
                  </AntForm.Item>
                </Col>
                <Col xs={6}>
                  <AntForm.Item name={"isDeveloper"}>
                    <Select
                      placeholder={t("IS_A_DEVELOPER")}
                      options={getIsDeveloperOptions()}
                      allowClear />
                  </AntForm.Item>
                </Col>
              </Row>
            </Col>

            <Col xs={24} lg={6}>
              <div className={"flex float-right gap-2"}>
                <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                  {t("CLEAR")}
                </AntButton>
                <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                  {t("SEARCH")}
                </AntButton>
              </div>
            </Col>
          </Row>
        </AntForm>
      </div>
      <Loading active={isLoading} transparent >
        <TableAdmin
          columns={columns}
          dataSource={userData.rows}
          scroll={{ x: 1000 }}
          pagination={pagination}
          onChange={handleChangeTable}
          rowKey="_id"
        />
      </Loading>
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(User);
