import React, { useMemo, useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { connect } from "react-redux";
import { Card, Tabs, message } from "antd";
import { useTranslation } from "react-i18next";
import { Bar<PERSON><PERSON>Outlined, DollarOutlined, ToolOutlined, BookOutlined, UserOutlined } from "@ant-design/icons";

import Filter from "./Filter";
import TotalCost from "./TotalCost";
import CostByTool from "./CostByTool";
import CostByInstructions from "./CostByInstructions";
import StudentToolCost from "./StudentToolCost";

import { convertQueryToObject } from "@src/common/functionCommons";
import { getOpenAICost, getTokenToolByUser, getTokenInstructionByUser, getStudentToolsCost } from "@services/Dashboard";

import "./OpenAICost.scss";
import { memberTypeOptions } from "@constant";

OpenAICost.propTypes = {};

function OpenAICost({ user }) {
  const { t } = useTranslation();
  const location = useLocation();

  const [activeTab, setActiveTab] = useState("1");
  const [isLoading, setIsLoading] = useState(false);

  // State for all tab data
  const [totalCostData, setTotalCostData] = useState({
    imageCost: { totalCost: 0, totalSubmits: 0 },
    mediaCost: { totalCost: 0, totalSubmits: 0 },
    textCost: { totalCost: 0, totalSubmits: 0 },
    shadowingCost: { totalCost: 0, totalSubmits: 0 },
  });
  const [costByToolData, setCostByToolData] = useState([]);
  const [costByInstructionsData, setCostByInstructionsData] = useState([]);
  const [studentToolCostData, setStudentToolCostData] = useState([]);

  const queryParams = useMemo(() => convertQueryToObject(location.search), [location.search]);

  // Fetch all data when query params change
  useEffect(() => {
    fetchAllData(queryParams);
  }, [queryParams]);

  // Function to fetch all data at once
  const fetchAllData = async (query) => {
    setIsLoading(true);
    try {
      // Fetch data for all tabs in parallel
      const [totalCostResponse, costByToolResponse, costByInstructionsResponse, studentToolCostResponse] = await Promise.allSettled([
        getOpenAICost(query),
        getTokenToolByUser(query),
        getTokenInstructionByUser(query),
        getStudentToolsCost(query)
      ]);

      // Update state with responses
      if (totalCostResponse.status === 'fulfilled' && totalCostResponse.value) {
        setTotalCostData(totalCostResponse.value);
      }

      if (costByToolResponse.status === 'fulfilled' && costByToolResponse.value) {
        setCostByToolData(costByToolResponse.value);
      }

      if (costByInstructionsResponse.status === 'fulfilled' && costByInstructionsResponse.value) {
        setCostByInstructionsData(costByInstructionsResponse.value);
      }

      if (studentToolCostResponse.status === 'fulfilled' && studentToolCostResponse.value) {
        setStudentToolCostData(studentToolCostResponse.value);
      }
    } catch (error) {
      console.error("Error fetching OpenAI cost data:", error);
      message.error(t("ERROR_FETCHING_DATA"));
    } finally {
      setIsLoading(false);
    }
  };

  const tabItems = [
    {
      label: t("TOTAL_COST"),
      key: "1",
      icon: <DollarOutlined />
    },
    {
      label: t("COST_BY_TOOL"),
      key: "2",
      icon: <ToolOutlined />
    },
    {
      label: t("COST_BY_INSTRUCTION"),
      key: "3",
      icon: <BookOutlined />
    },
    {
      label: t("STUDENT_TOOLS_COST"),
      key: "4",
      icon: <UserOutlined />
    },
  ];

  return (
    <div className="open-ai-cost-container">
      <Card className="openai-cost-info-card">
        <div className="openai-cost-info-header">
          <div>
            <h1 className="openai-cost-title">{t("OPENAI_COST_ANALYSIS")}</h1>
            <p className="openai-cost-description">{t("OPENAI_COST_ANALYSIS_DESCRIPTION")}</p>
          </div>
          <BarChartOutlined className="openai-cost-icon" />
        </div>
      </Card>

      <Card className="openai-cost-filter-card">
        <Filter />
      </Card>

      <Card className="openai-cost-tabs-card">
        <div className="tabs-header">
          <h2 className="tabs-title">{t("COST_ANALYSIS_VIEWS")}</h2>
          <p className="tabs-description">{t("COST_ANALYSIS_VIEWS_DESCRIPTION")}</p>
        </div>
        <Tabs
          activeKey={activeTab}
          items={tabItems}
          onChange={setActiveTab}
          className="open-ai__tab"
          tabBarGutter={8}
          size="middle"
          type="card"
          animated={{ inkBar: true, tabPane: false }}
        />
        <div className="openai-cost-tab-content">
          <TotalCost
            activeTab={activeTab}
            totalCostData={totalCostData}
            isLoading={isLoading}
          />
          <CostByTool
            activeTab={activeTab}
            costByToolData={costByToolData}
            isLoading={isLoading}
            queryParams={queryParams}
          />
          <CostByInstructions
            activeTab={activeTab}
            costByInstructionsData={costByInstructionsData}
            isLoading={isLoading}
            queryParams={queryParams}
          />
          <StudentToolCost
            activeTab={activeTab}
            studentToolCostData={studentToolCostData}
            isLoading={isLoading}
            queryParams={queryParams}
          />
        </div>
      </Card>
    </div>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(OpenAICost);
