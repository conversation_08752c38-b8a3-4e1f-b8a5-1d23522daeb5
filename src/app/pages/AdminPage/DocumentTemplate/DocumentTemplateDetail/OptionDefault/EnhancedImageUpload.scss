.enhanced-image-upload {
  width: 200px;
  height: 200px;
  padding: 8px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  flex-shrink: 0;
  transition: all 0.3s;

  &:hover {
    border-color: var(--primary-colours-blue-navy);
    box-shadow: 0 0 8px rgba(9, 25, 107, 0.1);
  }

  .enhanced-image-upload__inner {
    width: 100%;
    height: 100%;
    background: #FAFAFA;
    position: relative;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &:not(:hover) {
      .enhanced-image-upload__backdrop {
        opacity: 0;
        visibility: hidden;
      }
    }

    .enhanced-image-upload__backdrop {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, .6);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      
      .enhanced-image-upload__actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }

    .enhanced-image-upload__upload {
      cursor: pointer;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 12px;
      font-size: 16px;
      user-select: none;
      color: var(--typo-colours-support-grey-light);

      svg {
        width: 32px;
        height: 32px;
      }
      
      svg path {
        stroke: var(--typo-colours-support-grey-light);
      }
      
      &:hover {
        color: var(--primary-colours-blue-navy);
        
        svg path {
          stroke: var(--primary-colours-blue-navy);
        }
      }
    }
  }
}
