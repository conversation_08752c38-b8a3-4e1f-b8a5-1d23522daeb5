import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Form, Input } from "antd";

import { toast } from "@component/ToastProvider";
import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import NoData from "@component/NoData";
import EnhancedImageUpload from "./EnhancedImageUpload";

import { BUTTON } from "@constant";

import { uploadFile } from "@services/File";
import { createDefaultHeader, getDocDefaultHeader } from "@services/DocumentHeader";

import "./OptionDefault.scss";

function OptionDefault({ documentOptionsData }) {
  const { t } = useTranslation();
  const documentTemplateId = useParams().id;

  const [formOptionDefault] = Form.useForm();

  const [isLoading, setLoading] = useState(false);

  const [optionImageId, setOptionImageId] = useState(null);
  const [isLoadingImage, setLoadingImage] = useState(false);


  useEffect(() => {
    getDefaultHeaderData();
  }, [documentTemplateId]);

  async function handleClearImage() {
    setOptionImageId(null);
    formOptionDefault.resetFields(["imageId"]);
  }

  async function handleUploadImage(file) {
    setLoadingImage(true);
    const apiResponse = await uploadFile(file, { folder: "image" });
    if (apiResponse) {
      setOptionImageId(apiResponse._id);
      formOptionDefault.setFieldsValue({ imageId: apiResponse._id });
    }
    setLoadingImage(false);
  }

  async function getDefaultHeaderData() {
    setLoading(true);
    try {
      const apiResponse = await getDocDefaultHeader(documentTemplateId);
      if (apiResponse?.customHeader) {
        formOptionDefault.setFieldsValue(apiResponse.customHeader);
        if (apiResponse.customHeader.imageId) {
          setOptionImageId(apiResponse.customHeader.imageId);
        }
      }
    } catch (error) {
      console.error("Error loading default header data:", error);
    } finally {
      setLoading(false);
    }
  }


  async function onFinish(values) {
    setLoading(true);
    const apiRequest = {
      docxTemplateId: documentTemplateId,
      customHeader: values,
    };
    const apiResponse = await createDefaultHeader(apiRequest);
    if (apiResponse) {
      toast.success("SAVE_DATA_SUCCESS", { replace: true });
    }
    setLoading(false);
  }

  return <>
    <div className="document-template-detail__header">
      {t("OPTION_DEFAULT")}
    </div>


    <Loading active={isLoading} className="document-options">
      <AntForm
        form={formOptionDefault}
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
      >
        {!!documentOptionsData?.length
          ? <>
            <AntForm.Item label={t("HEADER_IMAGE")} className="header-image-upload">
              <EnhancedImageUpload
                loading={isLoadingImage}
                onDrop={handleUploadImage}
                onClear={handleClearImage}
                imageId={optionImageId}
              />
            </AntForm.Item>

            <AntForm.Item hidden name="imageId">
              <Input readOnly />
            </AntForm.Item>

            <div className="option-fields-container">
              <div className="option-fields-title">{t("FIELD_VALUES")}</div>
              <div className="option-fields-grid">
                {documentOptionsData
                  .map((option, index) => {
                    return <AntForm.Item
                      key={index}
                      label={option.fieldLabel}
                      name={option.fieldName}
                    >
                      <Input size="large" placeholder={t("ENTER_VALUE")} />
                    </AntForm.Item>;
                  })}
              </div>
            </div>
            <div className="flex flex-row-reverse">
              <AntButton size="large" htmlType="submit" type={BUTTON.DEEP_NAVY}>
                {t("SAVE")}
              </AntButton>
            </div>
          </>
          : <div className="document-template-detail__no-data">
            <NoData />
          </div>}
      </AntForm>

    </Loading>

  </>;
}

export default OptionDefault;
