.document-options {

  .document-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;

    @media screen and (max-width: 767.98px) {
      grid-template-columns: 1fr;
    }
  }

  .document-option-card {
    border: 1px solid var(--background-light-background-grey);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-level-1);

    &:hover {
      box-shadow: var(--shadow-level-2);
      border-color: var(--primary-colours-blue-navy-light-2);
    }

    .document-option-card__content {
      padding: 16px;
    }

    .document-option-card__header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .document-option-card__title {
        font-weight: 600;
        font-size: 16px;
        color: var(--typo-colours-primary-black);
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
      }

      .document-option-card__image-badge {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        background-color: var(--green-light-1);
        color: var(--green-dark);
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;

        svg {
          width: 14px;
          height: 14px;
        }
      }
    }

    .document-option-card__details {
      padding-top: 12px;
      border-top: 1px solid var(--background-light-background-grey);
    }

    .document-option-card__field {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .document-option-card__field-label {
        font-weight: 500;
        color: var(--typo-colours-support-grey-light);
        margin-right: 8px;
        min-width: 80px;
      }

      .document-option-card__field-value {
        color: var(--typo-colours-primary-black);
        word-break: break-word;
      }
    }
  }

  .document-template-detail__no-data {
    padding: 24px;
    background-color: var(--background-light-background-grey);
    border-radius: 8px;
  }
}
