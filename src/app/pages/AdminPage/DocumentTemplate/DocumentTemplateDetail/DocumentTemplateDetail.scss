.document-template-detail-container {
  background-color: var(--white);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;

  .document-template-detail__header {
    font-weight: 700;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--background-light-background-grey);
  }

  .document-template-detail__section {
    background-color: var(--white);
    border: 1px solid var(--background-light-background-grey);
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow-level-1);

    .document-template-detail__header {
      font-size: 16px;
      padding-bottom: 12px;
    }

    .document-template__file-name {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 0;

      .document-template__file-display {
        display: flex;
        align-items: center;
        gap: 8px;

        .document-template__file-icon {
          font-size: 24px;
        }

        .document-template__file-name-text {
          font-weight: 500;
        }
      }

      .document-template__file-empty {
        color: var(--typo-colours-support-grey-light);
        font-style: italic;
      }

      .document-template__file-action {
        display: flex;
        flex-direction: row;
        gap: 16px;

        @media screen and (max-width: 767.98px) {
          flex-direction: column;
          gap: 8px;
        }
      }

      @media screen and (max-width: 767.98px) {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }
    }
  }

  .form-document-template-detail {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 24px;

    @media screen and (max-width: 767.98px) {
      grid-template-columns: 1fr;
    }

    .ant-form-item {
      margin: 0;
    }

    .form-document-template-detail__submit {
      display: flex;
      align-self: end;
      justify-content: flex-end;
      grid-column-start: 2;

      @media screen and (max-width: 767.98px) {
        grid-column-start: 1;
        margin-top: 16px;
      }
    }
  }

  .document-template-detail__no-data .no-data-container .no-data {
    width: 80px;
  }

  .document-template-detail__save-button {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .document-options {
    .ant-table-wrapper {
      border-radius: 8px;
      overflow: hidden;
    }
  }

  .document-template-detail__public-switch {
    .ant-form-item {
      margin-top: 16px;
    }
  }
}
