import { useTranslation } from "react-i18next";
import { <PERSON> } from "react-router-dom";

import { useSpeaking } from "@app/pages/Student/Speaking";

import { LINK } from "@link";

import SPEAKING_FEEDBACK from "@src/asset/icon/speaking-feedback.svg";
import ARROW_UP_RIGHT from "@src/asset/icon/arrow/arrow-up-right.svg";

import "./SpeakingHeader.scss";
import { actions, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";
import { useDispatch } from "react-redux";

function SpeakingHeader() {
  const { t } = useTranslation();
  const { speakingId } = useSpeaking();
  const dispatch = useDispatch();
  
  if (speakingId) return null;
  
  return <div className="speaking-header">
    <div className="speaking-header__icon">
      <img src={SPEAKING_FEEDBACK} alt="" />
    
    </div>
    <div className="speaking-header__title">
      {t("SPEAKING_FEEDBACK")}
    </div>
    <div className="speaking-header__desc">
      {t("THE_SPEECHES_CREATED_RECENTLY")}
      <Link to={LINK.SPEAKING_SPEECHES} className="speaking-header__desc-link"
        onClick={() => dispatch(actions.trackCustomClick(TRACKING_ACTIONS.VIEW_SPEECH_LIST))}
      >
        {t("MY_SPEECHES")}
        <img src={ARROW_UP_RIGHT} className="speaking-header__desc-icon" alt="" />
      </Link>
    
    </div>
  
  </div>;
}

export default SpeakingHeader;