import { useTranslation } from "react-i18next";

import { useSpeaking } from "@app/pages/Student/Speaking";

import ARROW_UP_RIGHT from "@src/asset/icon/arrow/arrow-up-right.svg";

import "./UploadRecordedAudio.scss";
import { RECOGNIZE_STATUS } from "@constant";
import clsx from "clsx";


function UploadRecordedAudio() {
  const { t } = useTranslation();
  const { audioFileSelected, setShowUpload } = useSpeaking();
  const { speakingForm, recognizeStatus, disabledEdit } = useSpeaking();
  
  
  async function handleChangeFile() {
    if (recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING || disabledEdit) return;
    
    const { errorFields } = await speakingForm.validateFields();
    if (!!errorFields?.length) return;
    
    
    setShowUpload(true);
  }
  
  return <div className="speaking-upload">
    <div className="speaking-upload__title">
      {t("UPLOAD_RECORDED_AUDIO")}
    </div>
    <div className="speaking-upload__file-name">
      {audioFileSelected?.name}
    </div>
    <div
      className={clsx("speaking-upload__change-file", { "speaking-upload__change-file-disabled": recognizeStatus === RECOGNIZE_STATUS.RECOGNIZING || disabledEdit })}
      onClick={handleChangeFile}
    >
      {t("CHANGE_FILE")}
      <img src={ARROW_UP_RIGHT} className="speaking-header__desc-icon" alt="" />
    </div>
  </div>;
}

export default UploadRecordedAudio;