@import "src/app/styles/scroll";

.speaking-result {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  user-select: none;
  min-height: 50px;

  margin: 0 -15px;
  padding: 0 9px;
  //height: 40px;

  @extend .scrollbar;
  @extend .scrollbar-show;

  scrollbar-gutter: stable both-edges;

  .speaking-result__placeholder {
    line-height: 25px;
    color: #B3B3B3
  }

  .speaking-result__word {
    font-size: 18px;
    line-height: 25px;
    padding: 1px 3px;
    position: relative;

    >span {
      line-height: 22px;
    }

    .speaking-word__mispronunciation {
      background-color: #FF070C33;
    }

    .speaking-word__monotone {
      position: relative;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #FF8F0D;
      }
    }
  }

  .speaking-word__missing-break {
    width: 47px;
    height: 22px;
    background-color: #E8EEFF;
  }

  .speaking-word__unexpected-break {
    width: 47px;
    height: 22px;
    background-color: #FCE9FF;
  }
}

.speaking-word__popover.ant-popover .ant-popover-content .ant-popover-inner {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
  font-size: 12px;
  box-shadow: 0px 4px 10px 0px #00000033;
  min-width: 180px;
  max-height: 50vh;
  overflow-y: auto;
  @extend .scrollbar;
  @extend .scrollbar-show;

  .phoneme__excellent {
    color: #26D06D;
  }

  .phoneme__almost {
    color: #FF7D0B;
  }

  .phoneme__try-again {
    color: #FF0307;
  }

  .ant-popover-title {
    display: flex;
    flex-direction: column;
    gap: 8px;
    line-height: 24px;


    .speaking-word__overall-accuracy {
      font-size: 16px;
      font-weight: 700;
      color: #000000;
    }

    .speaking-word__phonetic-transcriptions {
      display: flex;
      gap: 32px;

      .transcription {
        display: flex;
        gap: 8px;
        font-size: 16px;
        line-height: 24px;

        &.transcription__user {
          color: #26D06D;
        }

        button {
          background: var(--puple-backgound);

          svg path {
            stroke: #3A18CE;
          }

          svg g path {
            stroke: unset;
          }

          &:active {
            background: #3A18CE;

            svg path {
              stroke: #FFF !important;
            }

            svg g path {
              fill: #FFF;
              stroke: unset !important;
            }
          }
        }
      }
    }
  }

  .ant-popover-inner-content {
    .speaking-word__phonemes-evaluation {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .phonemes-evaluation__item {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .item__divider {
          width: 100%;
          height: 1px;
          background-color: #D9D9D9;
        }

        .item__content {
          display: flex;
          line-height: 20px;

          .item__phoneme {
            width: 70px;
          }
        }
      }
    }
  }
}