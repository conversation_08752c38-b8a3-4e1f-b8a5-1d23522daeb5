@import "src/app/styles/scroll";

.speaking-container {
  padding: 32px;
  border-radius: 24px;
  background: radial-gradient(18.71% 33.59% at 50% 8.03%, #F4F3FF 0.01%, #FFFFFF 100%);
  box-shadow: 0 4px 20px 0 #0000001A;
  display: flex;
  justify-content: center;
  flex-direction: column;
  gap: 8px;

  &.speaking-container-has-feedback {
    .speaking-inner {
      max-width: unset;

      .speaking__body {
        grid-template-columns: repeat(3, minmax(0, 1fr));

        .speaking-input-container,
        .speaking-feedback-container {
          @extend .scrollbar;
          @extend .scrollbar-show;

          max-height: calc(100vh - 244px);
          scrollbar-gutter: stable both-edges;
          padding: 0 4px;
          margin: 0 -10px;
        }
      }
    }
  }

  .speaking-inner {
    width: 100%;
    max-width: 832px;
    align-self: center;

    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;

    .speaking-title {
      width: 100%;
      font-size: 22px;
      font-weight: 600;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;

      .auto-resize-input__extra {
        display: flex;
        align-items: center;
      }

      .ant-input {
        color: var(--navy);
        font-weight: 600;
        font-size: 22px;
        line-height: 30px;
        background-color: transparent;
        padding: 0 10px;
        border-color: transparent;
      }


      .ant-btn {
        &:not(:disabled):not(.ant-btn-disabled) {
          border-color: transparent;
        }

        .ant-btn-icon svg {
          width: 20px;
          height: 20px;
        }
      }
    }

    .speaking__body {
      //width: 832px;
      display: grid;
      gap: 16px;
      grid-template-columns: repeat(1, minmax(0, 1fr));

    }


    .speaking-input-container {
      grid-column: span 1/ span 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      font-family: Inter, serif;

      .speaking-topic__input {
        .ant-form-item {
          margin: 0;

          .ant-form-item-control-input-content .ant-input {
            background-color: transparent;
            border-width: 0;
            box-shadow: none;
            padding: 0;
            color: #000;
            border-radius: 0;

            &[disabled] {
              cursor: default;
            }
          }

          .ant-form-item-explain .ant-form-item-explain-error {
            padding: 0 !important;
            margin: 0 !important;
            background: transparent !important;
          }
        }
      }

      .speaking-topic__tag {
        color: var(--navy);

        .ant-btn {
          margin-left: 8px;
          padding: 5px 15px;
          border-radius: 8px;
        }
      }

      .speaking-section {
        display: flex;
        flex-direction: column;
        padding: 15px;
        gap: 8px;
        border-radius: 16px;
        border: 1px solid #DBDBDB;
        background-color: var(--white);


        .speaking-section__title {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .speaking-section__title-text {
            color: var(--navy);
            display: flex;
            align-items: center;
            font-weight: 500;
          }

          .speaking-section__title-extra {
            display: inline-block;
            font-size: 12px;
            line-height: 16px;
            padding: 4px 8px;
            border-radius: 8px;
            color: #318D62;
            background-color: #E5FFF3;
          }

          .speaking-section__title-action {
            height: 20px;
            display: flex;
            align-items: center;
            margin-left: auto;


            .ant-btn {
              gap: 4px;
              border-radius: 8px;
              font-size: 12px;

              //.ant-btn-icon svg {
              //  width: 20px;
              //  height: 20px;
              //}
            }
          }
        }

        .speaking-section__divider {
          height: 1px;
          background-color: #DBDBDB;
        }

        .speaking-record {
          display: flex;
          gap: 8px;

          &.speaking-record__centered {
            align-self: center;
          }
        }

        .speaking-topic__tag {
          display: flex;
          gap: 2px;
          font-size: 12px;
          line-height: 20px;
          align-items: center;

          .speaking-topic__tag-label {
            font-weight: 500;
            color: var(--primary-colours-blue-navy);

            &::after {
              content: ":";
            }
          }
        }
      }
    }

    .speaking-feedback-container {
      grid-column: span 2/ span 2;
    }
  }
}