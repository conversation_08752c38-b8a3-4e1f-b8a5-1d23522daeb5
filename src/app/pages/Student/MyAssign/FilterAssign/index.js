import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import { Checkbox } from "antd";

import { CONSTANT, STUDENT_TOOL_BASE_TYPE, TYPE_OF_TOOL } from "@constant";

import { getProjectStudentTagCategory } from "@services/Project";

import "./FilterAssign.scss";
import FillterEssay from "./FillterEssay";
import FillterTag from "./FillterTag";

function FilterAssign({ studentTools, onChangeFilter, studentProjectType }) {
  const { t } = useTranslation();

  const [documentType, setDocumentType] = useState([]);
  const [categories, setCategories] = useState({});
  const [tags, setTags] = useState([]);

  const [tagCategoryData, setTagCategoryData] = useState({});

  useEffect(() => {
    getTagCategoryData();
  }, []);

  useEffect(() => {
    onChangeFilter({ documentType, categories, tags });
  }, [documentType, categories, tags]);

  const isShowEssayFilter = useMemo(() => {
    return studentProjectType === CONSTANT.WRITING && !!studentTools?.writing.length;
  }, [studentProjectType, studentTools]);

  const isSelectedTask2 = useMemo(() => {
    return isShowEssayFilter
      && (categories?.[STUDENT_TOOL_BASE_TYPE.writing_2]?.length > 0
        || !categories?.[STUDENT_TOOL_BASE_TYPE.writing_1]
      );
  }, [isShowEssayFilter, categories]);

  const allTags = useMemo(() => {
    if (studentProjectType === CONSTANT.SPEAKING) {
      return tagCategoryData?.[STUDENT_TOOL_BASE_TYPE.speaking]?.tags || [];
    }
    if (isSelectedTask2) {
      return tagCategoryData?.[STUDENT_TOOL_BASE_TYPE.writing_2]?.tags || [];
    }
    return [];
  }, [studentProjectType, tagCategoryData, isSelectedTask2]);

  useEffect(() => {
    setTags([]);
  }, [allTags]);

  const getTagCategoryData = async () => {
    const type = studentProjectType === CONSTANT.SPEAKING ? "speaking" : "writing";
    const apiResponse = await getProjectStudentTagCategory({ type });
    if (apiResponse) {
      setTagCategoryData(apiResponse);
    }
  };

  function onChangeDocumentType(type, checked) {
    setDocumentType(pre => checked ? [...pre, type] : pre.filter(item => item !== type));
  }

  return <div className="filter-assign-container">
    <div className="filter-assign__header">
      {t("FILTERS")}
    </div>
    <div className="filter-assign__section">
      <div className="filter-assign__section-header">
        {t("DOCUMENT_TYPE")}
      </div>
      <div className="filter-assign__section-body">
        <Checkbox
          checked={documentType.includes(CONSTANT.MY_FILE.toLowerCase())}
          onChange={e => onChangeDocumentType(CONSTANT.MY_FILE.toLowerCase(), e.target.checked)}>
          {t("MY_FILE")}
        </Checkbox>
        <Checkbox
          checked={documentType.includes(CONSTANT.SHARE_WITH_ME.toLowerCase())}
          onChange={e => onChangeDocumentType(CONSTANT.SHARE_WITH_ME.toLowerCase(), e.target.checked)}>
          {t("SHARED_TO_ME")}
        </Checkbox>
      </div>
    </div>

    {isShowEssayFilter && <FillterEssay
      writingTools={studentTools.writing}
      tagCategoryData={tagCategoryData}
      categories={categories}
      setCategories={setCategories}
    />}

    {!!allTags.length && <FillterTag tags={tags} setTags={setTags} allTags={allTags} />}
  </div>;
}

const mapStateToProps = (state) => ({
  studentTools: state.tool.studentTools,
});

export default (connect(mapStateToProps, {})(FilterAssign));