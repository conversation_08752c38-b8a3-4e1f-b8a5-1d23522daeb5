.transcript-container {
  max-height: 400px; // Adjust as needed
  overflow-y: auto;
  padding: 10px;
  margin-top: 10px;
  background: #e7e5ff;
  width: 100%;

  .transcript-item {
    display: flex;
    gap: 10px;
    padding: 8px 0;
    width: 100%;
    align-items: center;
    justify-content: center;


    &.active {
      background-color: #e0f7fa; // Example active highlight color
      font-weight: bold;
      width: 100%;
    }

    &.inactive {
      width: 100%;
    }

    p {
      margin: 0;
    }
  }

  .transcript-divider {
    border-bottom: 1px dashed #eee;
    margin: 5px 0;
  }
}
