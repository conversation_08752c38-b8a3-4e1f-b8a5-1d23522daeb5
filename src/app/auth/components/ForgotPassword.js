import { Link } from "react-router-dom";
import { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Form, Input } from "antd";
import { useOutletContext } from 'react-router-dom';

import { AntForm } from "@src/app/component/AntForm";
import AntButton from "@component/AntButton";

import { BUTTON, CONSTANT } from "@constant";
import RULE from "@rule";

import { requestForgetPassword } from "@services/Auth";

import CHEVRON_DOWN from "@src/asset/icon/chevron/chevron-down.svg";

import * as auth from "@src/ducks/auth.duck";

function ForgotPassword() {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const values = Form.useWatch([], form);
  const { setAuthMessageProp, setCountSubmit } = useOutletContext();

  // const [disableSubmit, setDisableSubmit] = useState(true);

  // useEffect(() => {
  //   form.validateFields({ validateOnly: true })
  //       .then(
  //         () => setDisableSubmit(!values?.email),
  //         () => setDisableSubmit(true),
  //       );
  // }, [values]);

  const handleSubmit = async (data) => {
    const apiResponse = await requestForgetPassword(data);
    if (apiResponse?.code === 200 && apiResponse?.data) {
      setAuthMessageProp({
        authStatus: CONSTANT.SUCCESS,
        authTitle: "AUTH_TITLE_FORGOT_PASSWORD_SUCCESS",
        authMessage: "AUTH_MESSAGE_FORGOT_PASSWORD_SUCCESS",
        email: data.email
      });
    } else {
      setAuthMessageProp({
        authStatus: CONSTANT.ERROR,
        authTitle: apiResponse?.code === 403 ? "AUTHENTICATION_WARING" : "AUTHENTICATION_FAILED",
        authMessage: apiResponse?.message || "AN_ERROR_OCCURRED_PLEASE_CONTACT_THE_ADMINISTRATOR",
      });
    }
    setCountSubmit(pre => pre + 1);
  };

  const handleFocus = () => {
    form.setFields([{
      name: 'email',
      errors: [],
    }]);
  }

  return (<>
    <div className="auth-content__back-to">
      <img src={CHEVRON_DOWN} alt="" />
      <Link to="/auth">{t("BACK_TO_LOGIN")}</Link>
    </div>
    <div className="auth-content__title">{t("FORGOT_YOUR_PASSWORD")}</div>
    <AntForm
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{ email: "" }}
      className="auth-content__form"
    >
      <AntForm.Item
        name="email"
        validateTrigger="onBlur"
        rules={[RULE.REQUIRED, RULE.EMAIL]}
        validateFirst
      >
        <Input size="large" placeholder={t("ENTER_EMAIL")} onFocus={handleFocus} />
      </AntForm.Item>
    </AntForm>

    <AntButton
      block
      size="large"
      className="auth-content__submit"
      type={BUTTON.DEEP_NAVY}
      // disabled={disableSubmit}
      onClick={form.submit}
    >
      {t("SUBMIT_FORGOT_PASSWORD")}
    </AntButton>
  </>
  );
}

function mapStateToProps(store) {
  return {};
}

export default (connect(mapStateToProps, auth.actions)(ForgotPassword));
