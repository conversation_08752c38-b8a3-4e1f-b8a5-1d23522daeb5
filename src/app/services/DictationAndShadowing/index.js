import { API } from "@api";
import { createBase, deleteBase, getAllPaginationBase, getDetailBase, updateBase } from "@services/Base";
import axios from "axios";

export function getPaginationExercise(paging, query) {
  return getAllPaginationBase(API.DICTATION_AND_SHADOWING_EXERCISE, paging, query, ["name"]);
}

export function getExerciseDetail(id) {
  return getDetailBase(API.DICTATION_AND_SHADOWING_EXERCISE_ID, id, []);
}

export function updateExercise(data) {
  return updateBase(API.DICTATION_AND_SHADOWING_UPDATE_EXERCISE, data);
}

export function textToAudio(data) {
  return axios.post(API.DICTATION_AND_SHADOWING_CREATE_AUDIO, (data))
              .then(response => {
                if (response.status === 200) return response.data;
                return null;
              })
              .catch((err) => null);
  //return createBase(API.PUBLISH_DOCUMENT_TEMPLATE, data);
}

export function deleteExercise(id) {
  return deleteBase(API.DICTATION_AND_SHADOWING_DELETE_EXERCISE, id);
}

export function createExercise(data) {
  return createBase(API.DICTATION_AND_SHADOWING_CREATE_EXERCISE, data);
}

export function uploadExerciseAudio(file, config = {}) {
  config.headers = { "content-type": "multipart/form-data" };
  const formData = new FormData();

  formData.append("fileType", "file");
  formData.append("file", file);
  return axios.post(API.DICTATION_AND_SHADOWING_UPLOAD_AUDIO, formData, config)
              .then(response => {
                if (response.status === 200) return response.data;
                return null;
              })
              .catch(err => null);
}

