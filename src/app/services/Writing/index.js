import { API } from "@api";
import { createBase, deleteBase, getAllBase, updateBase, getAllManager, getDetailBase } from "@services/Base";
import { convertSnakeCaseToCamelCase } from "@src/common/dataConverter";
import axios from "axios";

export async function createWritingStudent(data) {
  // const config = { hideNoti: true };
  return axios.post(API.CREATE_WRITING_STUDENT, (data))
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

export async function submitWritingStudent(data) {
  // const config = { hideNoti: true };
  return axios.post(API.SUBMIT_WRITING_STUDENT, (data))
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

/**
 * Generate ideas for a topic
 *
 * @param {String} topic - The topic to generate ideas for
 * @param {String} taskType - The task type (task1, task2, general)
 * @param {String} topicImageId - The topic image ID (optional, for Task 1)
 * @returns {Object} The generated ideas
 */
export async function generateIdea(data) {
  return axios.post(API.EXPLAIN_GENERATE_IDEA, data)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      return null;
    });
}

/**
 * Help me understand the topic
 *
 * @param {Object} data - The request data
 * @param {String} data.topic - The topic to help understand
 * @param {String} data.taskType - The task type (task1, task2, general)
 * @param {String} data.topicImageId - The topic image ID (optional, for Task 1)
 * @returns {Object} The explanation response
 */
export async function helpMeUnderstand(data) {
  return axios.post(API.EXPLAIN_HELP_ME_UNDERSTAND, data)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      return null;
    });
}

/**
 * Help me write a specific section of an essay
 *
 * @param {Object} data - The request data
 * @param {String} data.topic - The topic to write about
 * @param {String} data.taskType - The task type (task1, task2)
 * @param {String} data.section - The section to write (introduction, body, conclusion, full)
 * @param {String} data.topicImageId - The topic image ID (optional, for Task 1)
 * @returns {Object} The writing response
 */
export async function helpMeWrite(data) {
  return axios.post(API.EXPLAIN_HELP_ME_WRITE, data)
    .then(response => {
      if (response.status === 200) return response?.data;
      return null;
    })
    .catch((err) => {
      return null;
    });
}

/**
 * Find vocabulary related to a topic
 *
 * @param {Object} data - The request data
 * @param {String} data.topic - The topic to find vocabulary for
 * @param {String} data.topicImageId - The topic image ID (optional, for Task 1)
 * @param {String} data.taskType - The task type (task1, task2, general)
 * @param {String} data.difficultyLevel - The difficulty level (e.g., expert)
 * @returns {Object | null} The vocabulary result or null on error
 */
export async function findVocabulary(data) {
  return axios.post(API.EXPLAIN_FIND_VOCABULARY, data)
    .then(response => {
      if (response.status === 200) return response?.data?.result;
      return null;
    })
    .catch((err) => {
      console.error("Error finding vocabulary:", err); // Thêm log lỗi để dễ debug
      return null;
    });
}

