import HeartEmpty from "@component/SvgIcons/Heart/HeartEmpty";
import i18next from "i18next";
import PaperOrange from "@component/SvgIcons/Paper/PaperOrange";
import EditSquareBlue from "@component/SvgIcons/Edit/EditSquareBlue";
import VoiceViolet from "@component/SvgIcons/Voice/VoiceViolet";
import VolumeUpRed from "@component/SvgIcons/Volume/VolumeUpRed";
import BuildingGreen from "@src/app/component/SvgIcons/Building/BuildingGreen";
import Work from "@src/app/component/SvgIcons/Work";
import READ_UNDERSTAND_ICON from "@src/asset/icon/toolGroupIcon/read_understand.svg";
import DRILL_GRAMMAR_ICON from "@src/asset/icon/toolGroupIcon/drill_grammar.svg";
import LEARN_NEW_WORDS_ICON from "@src/asset/icon/toolGroupIcon/learn_practice.svg";
import MAKE_SPEAK_ICON from "@src/asset/icon/toolGroupIcon/make_speak.svg";
import WATCH_LISTEN_ICON from "@src/asset/icon/toolGroupIcon/watch_listen.svg";
import HOMEWORK_ICON from "@src/asset/icon/toolGroupIcon/ideas_homework.svg";
import Speaking from "@app/pages/Student/Speaking";

export const LANGUAGE = { VI: "vi", EN: "en" };

export const SCREEN_PC = 1536;

export const CONSTANT = {
  LOGO_TEXT: "Clickee",
  MAX_DURATION: 1200,
  MAX_RECORD: 150,
  CHUNK_SIZE: 4096,
  PAGE_SIZE: 10,
  FILTER_PREFIX: "filter_",
  FILTERER: "FILTERER",
  YOUTUBE: "YOUTUBE",
  DOCUMENT: "DOCUMENT",
  IMAGE: "IMAGE",
  EXAM_QUESTION: "EXAM_QUESTION",
  LARGE: "LARGE",
  MEDIUM: "MEDIUM",
  SMALL: "SMALL",
  IN: "IN",
  OUT: "OUT",
  ANDROID: "ANDROID",
  IOS: "IOS",
  BARS: "BARS",
  PAGE: "PAGE",
  PREV: "PREV",
  NEXT: "NEXT",
  HIDDEN: "HIDDEN",
  EXISTS: "EXISTS",
  NOT_EXIST: "NOT_EXIST",
  INITIAL: "INITIAL",
  GET: "GET",
  PUT: "PUT",
  POST: "POST",
  LOGIN: "LOGIN",
  DEFAULT: "DEFAULT",
  ALL: "ALL",
  SUBMIT_ONE: "SUBMIT_ONE",
  SUBMIT_ALL: "SUBMIT_ALL",
  ALL_LOWER: "all",
  READ: "READ",
  DELETE: "DELETE",
  DELETE_MULTI: "DELETE_MULTI",
  EDIT: "EDIT",
  VIEW: "VIEW",
  CREATE: "CREATE",
  UPDATE: "UPDATE",
  ADD: "ADD",
  ADD_NEW: "ADD_NEW",
  REMOVE: "REMOVE",
  SAVE: "SAVE",
  CONFIRM: "CONFIRM",
  CANCEL: "CANCEL",
  CLOSE: "CLOSE",
  FAVORITE: "FAVORITE",
  
  TEXT: "TEXT",
  NUMBER: "NUMBER",
  MONTH: "MONTH",
  QUARTER: "QUARTER",
  YEAR: "YEAR",
  OTHER: "OTHER",
  DATE: "DATE",
  TIME: "TIME",
  DATE_TIME: "DATE_TIME",
  TIME_DATE: "TIME_DATE",
  FORMAT_DATE: "DD/MM/YYYY",
  FORMAT_DATE_TIME: "DD/MM/YYYY HH:mm",
  FORMAT_TIME_DATE: "HH:mm DD/MM/YYYY",
  INPUT: "INPUT",
  OUTPUT: "OUTPUT",
  CHECKBOX: "CHECKBOX",
  RADIO: "RADIO",
  SELECT: "SELECT",
  SELECT_MULTI: "SELECT_MULTI",
  TREE_SELECT: "TREE_SELECT",
  TEXT_AREA: "TEXT_AREA",
  PASSWORD: "PASSWORD",
  SWITCH: "SWITCH",
  LABEL: "LABEL",
  FILE: "FILE",
  
  DESTROY: "DESTROY",
  SUCCESS: "SUCCESS",
  ERROR: "ERROR",
  INFO: "INFO",
  WARNING: "WARNING",
  DENIED: "DENIED",
  
  RECORD: "RECORD",
  NOT_UPLOADED: "NOT_UPLOADED",
  UPLOADING: "UPLOADING",
  UPLOADED: "UPLOADED",
  UPLOAD_ERROR: "UPLOAD_ERROR",
  
  NOT_FOUND: "NOT_FOUND",
  RECTANGLE: "RECTANGLE",
  POLYGON: "POLYGON",
  POINT_SIZE: 8,
  
  DANGER: "DANGER",
  
  STOP: "STOP",
  START: "START",
  END: "END",
  LOCK: "LOCK",
  COMPLETE: "COMPLETE",
  PROCESSING: "PROCESSING",
  PROCESSED: "PROCESSED",
  POPULATE: "populate",
  DATE_TIME_FORMAT: "--:-- --/--/----",
  OPEN_STREET_MAP: "openStreetMap",
  
  LOG_OUT: "logout",
  AUTO_COMPLETE: "AUTO_COMPLETE",
  
  TOP: "TOP",
  
  CENTER: "CENTER",
  LEFT: "LEFT",
  RIGHT: "RIGHT",
  SATELLITE: "SATELLITE",
  TERRAIN: "TERRAIN",
  MAP: "MAP",
  
  DRAW: "DRAW",
  MOVE: "MOVE",
  
  POINT: "POINT",
  OBJECT: "OBJECT",
  ACTIVE: "ACTIVE",
  INACTIVE: "INACTIVE",
  NOT: "NOT",
  TRUE: "TRUE",
  FALSE: "FALSE",
  EVALUATION: "EVALUATION",
  
  DOWNLOAD: "DOWNLOAD",
  VIDEO: "VIDEO",
  YOUTUBE_VIDEO: "youtube",
  OFFLINE_VIDEO: "offline",
  DONE: "DONE",
  TOPIC: "TOPIC",
  AUDIO: "AUDIO",
  WORDS: "WORDS",
  INSTRUCTION: "INSTRUCTION",
  SAMPLE_EXAM: "SAMPLE_EXAM",
  
  MOVE_UP: "MOVE_UP",
  MOVE_DOWN: "MOVE_DOWN",
  
  LIKE: "like",
  DISLIKE: "dislike",
  
  HOUR: "HOUR",
  MINUTE: "MINUTE",
  SECOND: "SECOND",
  
  TOAST_DURATION: 3,
  YTB_EMBED_URL_START: "https://www.youtube.com/embed/{0}?start={1}",
  YTB_EMBED_URL: "https://www.youtube.com/embed/{0}?start={1}&end={2}",
  
  FOLDER_ID: "FOLDER_ID",
  PROJECT_ID: "PROJECT_ID",
  WORKSPACE_NAME: "WORKSPACE_NAME",
  EXAM_FOLDER: "EXAM_FOLDER",
  
  DOCX: "DOCX",
  PDF: "PDF",
  PERSONAL: "PERSONAL",
  SYSTEM: "SYSTEM",
  ORGANIZATION: "ORGANIZATION",
  
  MY_TEMPLATE: "MY_TEMPLATE",
  SYSTEM_TEMPLATE: "SYSTEM_TEMPLATE",
  ORG_TEMPLATE: "ORG_TEMPLATE",
  INCENTIVE: "incentive",
  NAME: "NAME",
  ADMIN: "admin",
  NORMAL: "normal",
  CONTRIBUTOR: "contributor",
  DESCRIPTION: "DESCRIPTION",
  
  LOGIN_GOOGLE: "LOGIN_GOOGLE",
  GOOGLE_FORM: "GOOGLE_FORM",
  
  MY_COMPUTER: "MY_COMPUTER",
  MY_RESOURCE: "MY_RESOURCE",
  ORG_RESOURCE: "ORG_RESOURCE",
  FOLDER: "FOLDER",
  PROJECT: "PROJECT",
  PROJECT_LESSON: "PROJECT_LESSON",
  PROJECT_EXAM: "PROJECT_EXAM",
  PROJECT_GRADING_ASSIGNMENT: "PROJECT_GRADING_ASSIGNMENT",
  FREE: "FREE",
  WEEK: "WEEK",
  
  EMPTY: "EMPTY",
  LINK: "LINK",
  UNLIMITED: "Unlimited",
  QUESTION: "QUESTION",
  ANSWER: "ANSWER",
  GB: "GB",
  ADDON: "addon",
  MINIUM_TRANSACTION: 10000,
  
  OLDEST: "OLDEST",
  NEWEST: "NEWEST",
  CONTENT: "CONTENT",
  NO_DATA: "NO_DATA",
  HEADER: "HEADER",
  FOOTER: "FOOTER",
  
  MISSING: "MISSING",
  QUESTION_TYPE: "QUESTION_TYPE",
  EXAM: "EXAM",
  LESSON: "LESSON",
  MARKING: "MARKING",
  EXAM_SCHOOL: "EXAM_SCHOOL",
  EXAM_IELTS: "EXAM_IELTS",
  
  SINGLE: "SINGLE",
  MULTI: "MULTI",
  
  REGENERATE: "REGENERATE",
  MARK_EXAM: "MARK_EXAM",
  MARK_STUDENT: "MARK_STUDENT",
  
  NOT_STARTED: "NOT_STARTED",
  IN_PROGRESS: "IN_PROGRESS",
  
  ERR_CANCELED: "ERR_CANCELED",
  AZURE: "AZURE",
  IELTS: "IELTS",
  SPEAKING: "SPEAKING",
  WRITING: "WRITING",
  LATEST: "LATEST",
  
  MY_FILE: "MY_FILE",
  SHARE_WITH_ME: "SHARE_WITH_ME",
};

export const DOCUMENT_TEMPLATE_TYPE = {
  BASE_TEMPLATE: "BASE_TEMPLATE",
  TEMPLATE: "TEMPLATE",
};

export const PAGINATION_INIT = Object.assign({}, {
  rows: [],
  paging: {
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  },
  query: {},
});

export const GENDER = {
  MALE: { value: "MALE", lang: "MALE" },
  FEMALE: { value: "FEMALE", lang: "FEMALE" },
  OTHER: { value: "OTHER", lang: "OTHER" },
};

export const TOAST_MESSAGE = {
  SUCCESS: {
    DEFAULT: "Thành công",
  },
  ERROR: {
    DEFAULT: "Có lỗi xảy ra. Vui lòng liên hệ quản trị viên",
    LOGIN: "Có lỗi trong quá trình đăng nhập",
    GET: "Có lỗi trong quá trình lấy dữ liệu",
    POST: "Có lỗi trong quá trình tạo mới",
    PUT: "Có lỗi trong quá trình cập nhật",
    DELETE: "Có lỗi trong quá trình xoá dữ liệu",
    DESCRIPTION: "Vui lòng kiểm tra và thử lại",
  },
  ICON: {
    SUCCESS: "",
    ERROR: "",
    INFO: "",
    WARNING: "",
  },
};

export const PAGINATION_CONFIG = Object.assign(
  {},
  {
    pageSizeOptions: ["5", "10", "20", "50"],
    showSizeChanger: true,
    // showTotal: (total, range) => `${range[0]}-${range[1]} của ${total}`,
  },
);

export const PAGINATION_MODAL = Object.assign({}, PAGINATION_CONFIG, {
  pageSizeOptions: [5, 10, 15],
  defaultPageSize: 5,
});

export const THEME_TYPE = {
  LIGHT: "LIGHT",
  DARK: "DARK",
};

export const LAYOUT_TYPE = {
  TABLE: "TABLE",
  GRID: "GRID",
};


export const GENERAL_ACCESS_TYPE = {
  ANYONE_WITH_LINK: "ANYONE_WITH_LINK",
  ORGANIZATIONAL: "ORGANIZATIONAL",
  RESTRICTED: "RESTRICTED",
};

export const ALPHABET = [
  "A",
  "B",
  "C",
  "D",
  "E",
  "F",
  "G",
  "H",
  "I",
  "J",
  "K",
  "L",
  "M",
  "N",
  "O",
  "P",
  "Q",
  "R",
  "S",
  "T",
  "U",
  "V",
  "W",
  "X",
  "Y",
  "Z",
];

export const PERMISSION_OPTIONS = {
  EDITOR: "EDITOR",
  VIEWER: "VIEWER",
  REMOVE_ACCESS: "REMOVE_ACCESS",
};

export const PERMISSION = {
  NO_PERMISSION: "NO_PERMISSION",
  OWNER: "OWNER",
  EDITOR: "EDITOR",
  VIEWER: "VIEWER",
};

export const FIELD_TYPE = {
  TEXT: "TEXT",
  TEXTAREA: "TEXTAREA",
  NUMBER: "NUMBER",
  SELECT: "SELECT",
  SELECT_MULTIPLE: "SELECT_MULTIPLE",
  TAG: "TAG",
};

export const INPUT_TYPE = [
  { value: "text", label: "Text" },
  { value: "textarea", label: "Textarea" },
  { value: "number", label: "Number" },
  { value: "select", label: "Select" },
  { value: "select_multiple", label: "Select multiple" },
  { value: "tag", label: "Tag" },
];

export const RULES = [{ value: "required", label: "Required" }];

export const TOOL_INPUT_TYPE = [
  { value: "topic", label: "Topic" },
  { value: "audio_stream", label: "Audio stream" },
  { value: "text", label: "Text" },
  { value: "video", label: "Video" },
  { value: "audio", label: "Audio" },
  { value: "image", label: "Image" },
  { value: "mark_test", label: "Mark test" },
  { value: "mark_test_task_1", label: "Mark test ielts task 1" },
  { value: "mark_test_task_2", label: "Mark test ielts task 2" },
  { value: "mark_test_image", label: "Mark test image" },
  { value: "words", label: "Words" },
  { value: "html", label: "Html" },
  { value: "html_trim_nbsp", label: "Html trim nbsp" },
  { value: "text_to_speech", label: "Text to speech" },
  { value: "none", label: "None" },
  { value: "student_task_1", label: "Student writing task 1" },
  { value: "student_task_2", label: "Student writing task 2" },
  { value: "student_speaking", label: "Student speaking" },
];

export const OUTPUT_TYPE = [
  { value: "text", label: "Text" },
  { value: "options", label: "Options" },
  { value: "html", label: "Html" },
  { value: "html_questions", label: "Html questions" },
  { value: "tf_question", label: "True/false question" },
  { value: "open_question", label: "Open question" },
  { value: "multi_choice", label: "Multiple choice" },
  { value: "fill_gaps", label: "Fill gaps" },
  { value: "dialogues", label: "Dialogues" },
  { value: "matching_words", label: "Matching words" },
  { value: "words", label: "Words" },
  { value: "scramble_words", label: "Scramble words" },
  { value: "writing_task", label: "Writing task" },
  { value: "advantages_and_disadvantages", label: "Advantages and disadvantages" },
  { value: "essay_topics", label: "Essay topics" },
];
export const RESPONSE_FORMAT = [
  { value: "json_object", label: "JSON object" },
  { value: "markdown", label: "Markdown" },
];

export const OWLLEE_CONFIG = {
  botService: "Salebot",
  botId: "661f755c3c1c570c9a889800",
  domainChat: "https://owllee.io",
  defaultBackgroundColor: "#3071FF",
  avatarDefault: "https://owllee.io/resources/files/chatbot/avatar/default.png",
};

export const ROLE_CONVERSATION = [
  { value: "system", label: "System" },
  { value: "user", label: "User" },
  { value: "assistant", label: "Assistant" },
];

export const BUTTON = {
  GHOST_BLUE: "ghost-blue",
  LIGHT_BLUE: "light-blue",
  DEEP_BLUE: "deep-blue",
  
  GHOST_NAVY: "ghost-navy",
  LIGHT_NAVY: "light-navy",
  LIGHT_NAVY2: "light-navy-2",
  DEEP_NAVY: "deep-navy",
  
  GHOST_COBALT: "ghost-cobalt",
  LIGHT_COBALT: "light-cobalt",
  DEEP_COBALT: "deep-cobalt",
  
  GHOST_YELLOW: "ghost-yellow",
  LIGHT_YELLOW: "light-yellow",
  DEEP_YELLOW: "deep-yellow",
  
  GHOST_GREEN: "ghost-green",
  LIGHT_GREEN: "light-green",
  DEEP_GREEN: "deep-green",
  
  GHOST_PURPLE: "ghost-purple",
  LIGHT_PURPLE: "light-purple",
  DEEP_PURPLE: "deep-purple",
  
  GHOST_PINK: "ghost-pink",
  LIGHT_PINK: "light-pink",
  DEEP_PINK: "deep-pink",
  
  GHOST_RED: "ghost-red",
  LIGHT_RED: "light-red",
  DEEP_RED: "deep-red",
  
  GHOST_WHITE: "ghost-white",
  WHITE: "white",
  WHITE_BLUE: "white-blue",
  WHITE_NAVY: "white-navy",
  WHITE_COBALT: "white-cobalt",
  WHITE_RED: "white-red",
};

export const CATEGORIES = [
  { value: "Reading", label: "Reading", normalType: BUTTON.LIGHT_YELLOW, activeType: BUTTON.DEEP_YELLOW },
  { value: "Writing", label: "Writing", normalType: BUTTON.LIGHT_GREEN, activeType: BUTTON.DEEP_GREEN },
  { value: "Speaking", label: "Speaking", normalType: BUTTON.LIGHT_PURPLE, activeType: BUTTON.DEEP_PURPLE },
  { value: "Listening", label: "Listening", normalType: BUTTON.LIGHT_RED, activeType: BUTTON.DEEP_RED },
];

export const PROJECT_TYPE = {
  NORMAL: "NORMAL",
  EXAM_SCHOOL: "EXAM_SCHOOL",
  EXAM_IELTS: "EXAM_IELTS",
  MARK_TEST_SCHOOL: "MARK_TEST_SCHOOL",
  MARK_TEST_IELTS: "MARK_TEST_IELTS",
};

export const TOOL_CATEGORY = {
  FAVORITE: {
    category: "favorite",
    lang: "FAVORITE",
    icon: <HeartEmpty/>,
    color: BUTTON.LIGHT_NAVY,
    activeColor: BUTTON.DEEP_NAVY,
  },
  READING: {
    category: "reading",
    lang: "READING",
    icon: <PaperOrange/>,
    color: BUTTON.LIGHT_YELLOW,
    activeColor: BUTTON.DEEP_YELLOW,
  },
  WRITING: {
    category: "writing",
    lang: "WRITING",
    icon: <EditSquareBlue/>,
    color: BUTTON.LIGHT_GREEN,
    activeColor: BUTTON.DEEP_GREEN,
  },
  SPEAKING: {
    category: "speaking",
    lang: "SPEAKING",
    icon: <VoiceViolet/>,
    color: BUTTON.LIGHT_PURPLE,
    activeColor: BUTTON.DEEP_PURPLE,
  },
  LISTENING: {
    category: "listening",
    lang: "LISTENING",
    icon: <VolumeUpRed/>,
    color: BUTTON.LIGHT_RED,
    activeColor: BUTTON.DEEP_RED,
  },
  VOCABULARY: {
    category: "vocabulary",
    lang: "VOCABULARY",
    icon: <PaperOrange/>,
    color: BUTTON.LIGHT_YELLOW,
    activeColor: BUTTON.DEEP_YELLOW,
  },
  GRAMMAR: {
    category: "grammar",
    lang: "GRAMMAR",
    icon: <Work/>,
    color: BUTTON.LIGHT_NAVY,
    activeColor: BUTTON.DEEP_NAVY,
  },
  ORGANIZATION: {
    category: "organization",
    icon: <BuildingGreen/>,
    color: BUTTON.LIGHT_GREEN,
    activeColor: BUTTON.DEEP_GREEN,
  },
};

export const SHOW_ON_WELCOME = [
  { value: "TEACHER_FOR_GRADES_1_2_3", label: "Elementary English teacher for grades 1-3" },
  { value: "LECTURER_AT_UNIVERSITY", label: "English lecturer at university, college" },
  { value: "LANGUAGE_CENTER", label: "English teacher at language center" },
  { value: "IELTS_CENTER", label: "English teacher at an IELTS, TOEFL center, etc..." },
  { value: "INDEPENDENT_TEACHER", label: "Indenpendent English teacher" },
  { value: "ENGLISH_CENTER_MANAGER", label: "English center manager" },
  { value: "OTHER", label: "Other" },
];

export const VISIBLE_TOOL = {
  public: { value: "public", label: "Public" },
  private: { value: "private", label: "Private" },
  developing: { value: "developing", label: "Developing" },
};

export const TOOL_TYPE = {
  EXAM_SCHOOL: { value: "EXAM_SCHOOL", label: "Exam school" },
  EXAM_IELTS: { value: "EXAM_IELTS", label: "Exam ielts" },
  MARK_TEST_SCHOOL: { value: "MARK_TEST_SCHOOL", label: "Mark test school" },
  MARK_TEST_IELTS: { value: "MARK_TEST_IELTS", label: "Mark test ielts" },
  NORMAL: { value: "NORMAL", label: "Normal" },
};

export const WORKSPACE_TYPE = {
  ORGANIZATIONAL: "ORGANIZATIONAL",
  PERSONAL: "PERSONAL",
};

export const TOOL_GROUP_ICON = {
  READ_UNDERSTAND: READ_UNDERSTAND_ICON,
  DRILL_GRAMMAR: DRILL_GRAMMAR_ICON,
  LEARN_NEW_WORDS: LEARN_NEW_WORDS_ICON,
  MAKE_SPEAK: MAKE_SPEAK_ICON,
  WATCH_LISTEN: WATCH_LISTEN_ICON,
  HOMEWORK: HOMEWORK_ICON,
  MARK_TEST_WRITING: READ_UNDERSTAND_ICON,
  MARK_TEST_LISTENING: MAKE_SPEAK_ICON,
};

export const PACKAGE_TERM = {
  [CONSTANT.MONTH]: 31,
  [CONSTANT.YEAR]: 365,
};

export const DISCOUNT_TYPE = {
  PERCENTAGE: "percentage",
  FIXED: "fixed",
};

export const PROMOTION_TYPE = {
  PERCENTAGE: { value: "percentage", lang: "PERCENTAGE" },
  FIXED: { value: "fixed", lang: "FIXED" },
  BONUS: { value: "bonus", lang: "BONUS" },
};

export const PAYMENT_STATUS = {
  DONE: "done",
  PROCESSING: "processing",
  ERROR: "error",
};

export const VNPAY_RESPONSE_ERROR = {
  "07": "SUSPECTED_TRANSACTION",
  "09": "NOT_REGISTER_INTERNETBANKING_TRANSACTION",
  "10": "VERIFIED_INCORRECT_TRANSACTION",
  "11": "TIMEOUT_TRANSACTION",
  "12": "LOCKED_ACCOUNT_TRANSACTION",
  "13": "WRONG_OTP_TRANSACTION",
  "24": "CANCELED_TRANSACTION",
  "51": "NOT_ENOUGH_BALANCE_TRANSACTION",
  "65": "LIMIT_EXCEEDED_TRANSACTION",
  "75": "BANK_MAINTENANCE",
  "79": "INCORRECT_PASSWORD_TRANSACTION",
  "99": "PAYMENT_FAILED",
};

export const TRANSACTION_STATUS = {
  DONE: "done",
  ERROR: "error",
  PROCESSING: "processing",
};

export const UPLOAD_STATUS = {
  PENDING: "PENDING",
  UPLOADING: "UPLOADING",
  SUCCESS: "SUCCESS",
  ERROR: "ERROR",
};
export const INVITATION_STATUS = {
  "pending": { value: "pending", lang: "INVITATION_PENDING" },
  "accepted": { value: "accepted", lang: "INVITATION_ACCEPTED" },
  "rejected": { value: "rejected", lang: "INVITATION_REJECTED" },
};

export const PACKAGE_TYPE = {
  BASE: {
    value: "base",
    label: "Base",
  },
  ADDON: {
    value: "addon",
    label: "Addon",
  },
};

export const PACKAGE_CUSTOMER_TARGET = {
  STUDENT: {
    value: "student",
    label: "Student",
  },
  TEACHER: {
    value: "teacher",
    label: "Teacher",
  },
  OTHER: {
    value: "other",
    label: "Other",
  },
};

export const CURRENCY_TYPE = [
  {
    value: "vnd",
    label: "VND",
  },
  {
    value: "usd",
    label: "USD",
  },
];

export const PRICE_TYPE = [
  {
    value: "day",
    label: "Day",
  },
  {
    value: "month",
    label: "Month",
  },
  {
    value: "year",
    label: "Year",
  },
];

export const LANG_OPTIONS = [
  {
    value: "en",
    label: "En",
  },
  {
    value: "vi",
    label: "Vi",
  },
];

export const TYPE_OF_TOOL = {
  OFFLINE_VIDEO: "offline_video",
  VIDEO: "video",
  AUDIO: "audio",
  TEXT: "text",
  TOPIC: "topic",
  WORDS: "words",
  IMAGE: "image",
  MARK_TEST: "mark_test",
  MARK_TEST_TASK_2: "mark_test_task_2",
  MARK_TEST_IMAGE: "mark_test_image",
  MARK_TEST_TASK_1: "mark_test_task_1",
  HTML: "html",
  HTML_TRIM_NBSP: "html_trim_nbsp",
  TEXT_TO_SPEECH: "text_to_speech",
  AUDIO_STREAM: "audio_stream",
  STUDENT_TASK_1: "student_task_1",
  STUDENT_TASK_2: "student_task_2",
  STUDENT_SPEAKING: "student_speaking",
};

export const OPTION_TOOL_WELCOME = [
  {
    value: "VIDEO",
    label: "Video",
  },
  {
    value: "AUDIO",
    label: "Audio",
  },
  {
    value: "IMAGE",
    label: "Image",
  },
  {
    value: "DOCUMENT",
    label: "Document",
  },
];
export const MAX_PAGE_RANGER = 5;

export const PREVIEW_TYPE = {
  ALL: "ALL",
  ANSWER_IN_BOTTOM: "ANSWERS_IN_BOTTOM",
  WITHOUT_ANSWER: "WITHOUT_ANWSERS",
};

export const getActivitiesActionOptions = () => [
  {
    value: "create",
    label: i18next.t("CREATE"),
  },
  {
    value: "update",
    label: i18next.t("UPDATE"),
  },
  {
    value: "delete",
    label: i18next.t("DELETE"),
  },
  {
    value: "copy",
    label: i18next.t("COPY"),
  },
  {
    value: "move",
    label: i18next.t("MOVE"),
  },
  {
    value: "share",
    label: i18next.t("SHARE"),
  },
  {
    value: "unshare",
    label: i18next.t("UNSHARE"),
  },
  {
    value: "download",
    label: i18next.t("DOWNLOAD"),
  },
  {
    value: "upload",
    label: i18next.t("UPLOAD"),
  },
];


export const ORG_TABS = {
  ORGANIZATION: "ORGANIZATION",
  ACTIVITY: "ACTIVITY",
  TRACKING: "TRACKING",
  TEMPLATE: "TEMPLATE",
};

export const MARK_EXAM_TOOL_TYPE = {
  HIGHT_SCHOOL: "MARK_TEST_SCHOOL",
  IELTS: "MARK_TEST_IELTS",
};

export const getExamCodeOptions = () => [
  { label: i18next.t("GRADE_3"), value: "grade_3" },
  { label: i18next.t("GRADE_4"), value: "grade_4" },
  { label: i18next.t("GRADE_5"), value: "grade_5" },
  { label: i18next.t("GRADE_6"), value: "grade_6" },
  { label: i18next.t("GRADE_7"), value: "grade_7" },
  { label: i18next.t("GRADE_8"), value: "grade_8" },
  { label: i18next.t("GRADE_9"), value: "grade_9" },
  { label: i18next.t("GRADE_10"), value: "grade_10" },
  { label: i18next.t("GRADE_11"), value: "grade_11" },
  { label: i18next.t("GRADE_12"), value: "grade_12" },
];

export const getProjectTypeOptions = () => [
  { label: i18next.t("HIGH_SCHOOL_PROGRAM"), value: "EXAM_SCHOOL" },
  { label: i18next.t("IELTS_PROGRAM"), value: "EXAM_IELTS", disabled: true },
];

export const getGradingTypeOptions = () => [
  { label: i18next.t("HIGH_SCHOOL_GRADING"), value: "MARK_TEST_SCHOOL" },
  { label: i18next.t("IELTS_GRADING"), value: "MARK_TEST_IELTS" },
];

export const getTrueFalseOptions = () => [
  { label: i18next.t("TRUE"), value: "true" },
  { label: i18next.t("FALSE"), value: "false" },
];

export const getIsDeveloperOptions = () => [
  { label: i18next.t("IS_A_DEVELOPER"), value: "true" },
  { label: i18next.t("IS_NOT_A_DEVELOPER"), value: "false" },
];
export const memberTypeOptions = () => [
  { label: i18next.t("THINKLABS_MEMBER"), value: "true" },
  { label: i18next.t("OTHER"), value: "false" },
];


export const RECORDING_STATUS = {
  RECORDING: "RECORDING",
  STOPPED: "STOPPED",
  WAITING: "WAITING",
};


export const RECORD_STATUS = {
  RECORDING: "RECORDING",
  STOPPED: "STOPPED",
  PREPARE_RECORD: "PREPARE_RECORD",
};


export const RECOGNIZE_STATUS = {
  NOT_STARTED: "NOT_STARTED",
  RECOGNIZING: "RECOGNIZING",
  COMPLETE: "COMPLETE",
};

export const USER_TYPE = {
  STUDENT: "student",
  TEACHER: "teacher",
};

export const SPEAKING_ERROR = {
  MISPRONUNCIATION: {
    key: "mispronunciation",
    lang: "MISPRONUNCIATIONS",
    descLang: "MISPRONUNCIATION_DESC",
    backgroundColor: "#FFD7D7",
    color: "#FF070C",
  },
  UNEXPECTED_BREAK: {
    key: "unexpectedBreak",
    lang: "UNEXPECTED_BREAK",
    descLang: "UNEXPECTED_BREAK_DESC",
    backgroundColor: "#FCE9FF",
    color: "#C658FB",
  },
  MISSING_BREAK: {
    key: "missingBreak",
    lang: "MISSING_BREAK",
    descLang: "MISSING_BREAK_DESC",
    backgroundColor: "#E8EEFF",
    color: "#316BFF",
  },
  MONOTONE: {
    key: "monotone",
    lang: "MONOTONE",
    descLang: "MONOTONE_DESC",
    backgroundColor: "#FFE9D0",
    color: "#FF8F0D",
  },
};


export const PRICING_TYPE = {
  INDIVIDUAL: "INDIVIDUAL",
  BUSINESS: "BUSINESS",
};
export const STUDENT_TOOL_BASE_TYPE = {
  writing_1: "writing_1",
  writing_2: "writing_2",
  speaking: "speaking",
};
export const FEEDBACK_TYPE = {
  ICON: "icon",
  NUMBER: "number",
  BOOL: "bool",
  COMMENT: "comment",
};

export const PAYMEN_METHOD = {
  CASH: { lang: "PAYMENT_IN_CASH" },
  VNPAY: { lang: "PAYMENT_VIA_VNPAY" },
};

export const DIFFICULTY_OPTIONS = [
  { value: "A1", label: "A1" },
  { value: "A2", label: "A2" },
  { value: "B1", label: "B1" },
  { value: "B2", label: "B2" },
  { value: "C1", label: "C1" },
  { value: "C2", label: "C2" },
];
export const EXERCISE_TYPE_OPTIONS = [
  { value: "dictation", label: "Dictation" },
  { value: "shadowing", label: "Shadowing" },
  { value: "dictation_shadowing", label: "Dictation & Shadowing" },
];
export const EXERCISE_STATUS_OPTIONS = [
  { value: "draft", label: "Draft" },
  { value: "published", label: "Published" },
  { value: "hidden", label: "Hidden" },
];