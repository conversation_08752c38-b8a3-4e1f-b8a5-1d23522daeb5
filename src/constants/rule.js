const RULE = {
  REQUIRED: { required: true, whitespace: true, message: "CAN_NOT_BE_BLANK", lang: "CAN_NOT_BE_BLANK" },
  NUMBER: { pattern: "^[0-9]+$", message: "NOT_IS_NUMBER" },
  POSITIVE_INTEGER: { pattern: "^[1-9][0-9]*$", message: "MUST_BE_POSITIVE_INTEGER" },
  PHONE: { pattern: "^[0-9]+$", len: 10, message: "INVALID_PHONE_NUMBER", lang: "INVALID_PHONE_NUMBER" },
  FAX: { pattern: "^[0-9]+$", min: 10, max: 11, message: "INVALID_FAX_NUMBER" },
  CMND: { required: true, pattern: "^[0-9]+$", message: "INVALID_ID_CARD_NUMBER" },
  EMAIL: { type: "email", message: "INVALID_EMAIL", lang: "INVALID_EMAIL" },
  NUMBER_FLOAT: {
    pattern: new RegExp("^[- +]?[0-9]+[.]?[0-9]*([eE][-+]?[0-9]+)?$"),
    message: "NOT_A_NUMBER",
  },
  PASSWORD_FORMAT: {
    pattern: new RegExp("^.{6,}$"),
    message: "RULE_PASSWORD_MESSAGE",
  },
  PASSWORD_FORMAT_1: {
    pattern: new RegExp("^(?!.* )(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^\\da-zA-Z])(.{8,})$"),
    message: "RULE_PASSWORD_MESSAGE",
  },
  PASSWORD_SYSADMIN_FORMAT: {
    pattern: new RegExp("^(?!.* )(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^\\da-zA-Z])(.{16,})$"),
    message: "RULE_PASSWORD_SYSADMIN_MESSAGE",
  },
  USERNAME_RANGER: {
    pattern: new RegExp("^([a-zA-Z0-9_-]){6,32}$"),
    message: "RULE_USERNAME_RANGER_MESSAGE",
  },
  USERNAME_LENGTH: {
    pattern: new RegExp("^(?!.* )(?=.{6,32})"),
    message: "RULE_USERNAME_LENGTH_MESSAGE",
  },
  TIME_FORMAT: {
    pattern: new RegExp("^([0-1]?\\d|2[0-3])(?::([0-5]?\\d))?(?::([0-5]?\\d))?$"),
    message: "RULE_TIME_FORMAT_MESSAGE",
  },
  YOUTUBE_URL: {
    pattern: new RegExp("http(?:s?):\\/\\/(?:www\\.)?youtu(?:be\\.com\\/watch\\?v=|\\.be\\/)([\\w\\-\\_]*)(&(amp;)?‌​[\\w\\?‌​=]*)?"),
    message: "INVALID_ADDRESS",
  },
};

export default RULE;
